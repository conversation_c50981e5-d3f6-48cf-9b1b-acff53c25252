<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Universities - Go Abroad</title>
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Leaflet CSS for Map View -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/modern-styles.css">
    <style>
        /* Page-specific styles */
        :root {
            --gradient-blue-purple: linear-gradient(135deg, #00c6ff, #7F00FF);
            --border-radius: 16px;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }

        .page-header {
            background: var(--gradient-blue-purple);
            color: var(--white);
            padding: 11rem 0 3rem; /* Increased top padding for sticky dual headers */
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
            opacity: 0.4;
            z-index: 0;
        }

        .page-header-content {
            position: relative;
            z-index: 1;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(to right, var(--white), rgba(255, 255, 255, 0.8));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            display: inline-block;
        }

        .page-description {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            max-width: 700px;
        }

        /* Region Tabs Styles */
        .region-tabs-container {
            background: var(--gradient-blue-purple);
            border-radius: var(--border-radius);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            margin-top: -2rem;
            position: relative;
            z-index: 10;
            overflow: hidden;
        }

        .region-tabs-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .region-tabs {
            display: flex;
            gap: 0.25rem;
            margin-bottom: 0;
            position: relative;
            z-index: 2;
            overflow-x: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            scroll-behavior: smooth;
            padding: 0.5rem;
            flex-grow: 1;
            mask-image: linear-gradient(to right, transparent, black 5%, black 95%, transparent);
            -webkit-mask-image: linear-gradient(to right, transparent, black 5%, black 95%, transparent);
        }

        .region-tabs::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .region-tab {
            background-color: rgba(255, 255, 255, 0.15);
            color: var(--white);
            border: none;
            padding: 0.75rem 1.25rem;
            border-radius: 999px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 600;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
            min-width: 120px;
            justify-content: center;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            white-space: nowrap;
            flex-shrink: 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .region-tab:hover {
            background-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        .region-tab.active {
            background-color: var(--white);
            color: #7F00FF;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .region-flag {
            font-size: 1.2rem;
        }

        .region-tabs-nav {
            display: flex;
            gap: 0.5rem;
            margin-left: 0.5rem;
        }

        .region-tabs-nav-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            flex-shrink: 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .region-tabs-nav-btn:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .region-tabs-nav-btn:active {
            transform: scale(0.95);
        }

        /* Country Tabs Styles */
        .country-tabs-container {
            background: linear-gradient(135deg, #00c9a7, #007bff);
            border-radius: var(--border-radius);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 9;
            overflow: hidden;
        }

        .country-tabs-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .country-tabs {
            display: flex;
            gap: 0.25rem;
            margin-bottom: 0;
            position: relative;
            z-index: 2;
            overflow-x: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            scroll-behavior: smooth;
            padding: 0.5rem;
            flex-grow: 1;
            mask-image: linear-gradient(to right, transparent, black 5%, black 95%, transparent);
            -webkit-mask-image: linear-gradient(to right, transparent, black 5%, black 95%, transparent);
        }

        .country-tabs::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .country-tab {
            background-color: rgba(255, 255, 255, 0.15);
            color: var(--white);
            border: none;
            padding: 0.75rem 1.25rem;
            border-radius: 999px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 600;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
            min-width: 120px;
            justify-content: center;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            white-space: nowrap;
            flex-shrink: 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .country-tab:hover {
            background-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        .country-tab.active {
            background-color: var(--white);
            color: #007bff;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .country-flag {
            font-size: 1.2rem;
        }

        .country-tabs-nav {
            display: flex;
            gap: 0.5rem;
            margin-left: 0.5rem;
        }

        .country-tabs-nav-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            flex-shrink: 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .country-tabs-nav-btn:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .country-tabs-nav-btn:active {
            transform: scale(0.95);
        }

        /* Make tabs responsive */
        @media (max-width: 768px) {
            .region-tabs-container {
                padding: 0.75rem;
            }

            .region-tab {
                padding: 0.6rem 1rem;
                min-width: 100px;
                font-size: 0.85rem;
            }

            .region-tabs-nav-btn {
                width: 32px;
                height: 32px;
            }

            .university-card {
                margin-bottom: 1.5rem;
            }

            .universities-grid {
                grid-template-columns: 1fr;
            }

            .search-filter-container .btn {
                padding: 0.5rem;
                font-size: 0.85rem;
            }

            .search-filter-container .btn i {
                margin-right: 0;
            }

            .search-filter-container .btn span {
                display: none;
            }

            .page-header {
                padding: 13rem 0 2rem; /* Adjusted for sticky dual headers */
            }

            .page-title {
                font-size: 2rem;
            }

            .country-rotator {
                order: 2;
                flex: 1;
                height: 60px;
                margin-left: 15px;
            }

            .country-item {
                min-width: 150px;
                gap: 12px;
                padding: 8px 12px;
            }

            .country-flag-single {
                width: 50px;
                height: 38px;
            }

            .country-code {
                font-size: 1.2rem;
            }

            .country-full-name {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 576px) {
            .country-rotator {
                flex: 1;
                height: 50px;
                margin-left: 10px;
            }

            .country-item {
                min-width: 130px;
                gap: 10px;
                padding: 6px 10px;
            }

            .country-flag-single {
                width: 45px;
                height: 34px;
            }

            .country-code {
                font-size: 1.1rem;
            }

            .country-full-name {
                font-size: 0.8rem;
            }
        }

        /* Search and Filter Styles */
        .search-filter-container {
            margin-bottom: 1.5rem;
            background-color: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .search-bar {
            position: relative;
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 1;
        }

        #university-search {
            padding-left: 40px;
            border-radius: 999px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            height: 48px;
        }

        #university-search:focus {
            border-color: #7F00FF;
            box-shadow: 0 0 0 3px rgba(127, 0, 255, 0.1);
        }

        .region-content {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: 2rem;
            margin-top: 2rem;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            padding: 0.75rem 1rem;
            background-color: var(--gray-100);
            border-radius: var(--radius-md);
        }

        .breadcrumb-item {
            color: var(--gray-600);
            cursor: pointer;
            transition: color 0.3s var(--transition-ease);
        }

        .breadcrumb-item:hover {
            color: var(--primary);
        }

        .breadcrumb-item.active {
            color: var(--primary);
            font-weight: 600;
        }

        .breadcrumb-separator {
            color: var(--gray-400);
        }

        .country-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: var(--white);
            padding: 1rem 1.5rem;
            border-radius: var(--radius-md);
            margin-bottom: 1.5rem;
            box-shadow: var(--shadow-md);
        }

        .universities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        /* University Card Styles */
        .university-card {
            position: relative;
            padding: 1.5rem;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 20px rgba(0, 123, 255, 0.08);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
            border: 1px solid rgba(0, 123, 255, 0.1);
            overflow: hidden;
        }

        .university-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--gradient-blue-purple);
        }

        .university-card .btn {
            margin-top: auto;
        }

        .university-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 123, 255, 0.15);
            border-color: #007bff;
        }

        .university-actions {
            position: absolute;
            top: 1rem;
            right: 1rem;
            z-index: 2;
            display: flex;
            gap: 0.5rem;
        }

        .btn-bookmark, .btn-compare-small {
            background: rgba(255, 255, 255, 0.95);
            border: none;
            color: #007bff;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-bookmark:hover, .btn-compare-small:hover {
            background: #f0f8ff;
            transform: scale(1.1);
            color: #0056b3;
        }

        .btn-bookmark.active {
            color: #7F00FF;
        }

        .btn-bookmark.active i {
            font-weight: 900;
        }

        .btn-compare-small.active {
            color: #7F00FF;
        }

        .university-location {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .university-actions-footer {
            margin-top: auto;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .action-buttons-row {
            display: flex;
            gap: 0.5rem;
        }

        /* University Requirements Styles */
        .university-requirements {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.2rem;
        }

        .requirements-title {
            font-size: 1rem;
            font-weight: 600;
            color: #212529;
            margin-bottom: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .requirements-title::before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 16px;
            background: linear-gradient(135deg, #007bff, #00c9a7);
            border-radius: 2px;
        }

        .requirements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.8rem;
        }

        .requirement-item {
            display: flex;
            align-items: flex-start;
            gap: 0.6rem;
        }

        .requirement-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 201, 167, 0.1));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007bff;
            flex-shrink: 0;
        }

        .requirement-content {
            flex-grow: 1;
        }

        .requirement-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 0.2rem;
        }

        .requirement-value {
            font-size: 0.9rem;
            color: #212529;
            font-weight: 500;
        }

        .university-rank {
            position: absolute;
            top: 1rem;
            left: 1rem;
            z-index: 2;
        }

        .rank-badge {
            display: inline-block;
            padding: 0.4rem 0.9rem;
            background: linear-gradient(135deg, #007bff, #00c9a7);
            color: white;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            box-shadow: 0 2px 6px rgba(0, 123, 255, 0.2);
        }

        .university-features {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .university-feature {
            display: inline-flex;
            align-items: center;
            gap: 0.35rem;
            padding: 0.25rem 0.5rem;
            background-color: #f8f9fa;
            border-radius: 999px;
            font-size: 0.75rem;
            color: #495057;
        }

        .university-feature i {
            color: #7F00FF;
            font-size: 0.8rem;
        }

        .universities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .highlight-card {
            animation: highlight-pulse 2s ease-in-out;
            border-color: var(--primary);
            box-shadow: 0 0 0 5px rgba(37, 99, 235, 0.3);
        }

        @keyframes highlight-pulse {
            0% { box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(37, 99, 235, 0); }
            100% { box-shadow: 0 0 0 0 rgba(37, 99, 235, 0); }
        }

        /* City Buttons Styles */
        .cities-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .city-button {
            background-color: white;
            border: 1px solid rgba(0, 123, 255, 0.1);
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 6px 12px rgba(0, 123, 255, 0.05);
            min-width: 180px;
        }

        .city-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(0, 123, 255, 0.1);
            border-color: #007bff;
        }

        .city-button.active {
            background: linear-gradient(to right, #f0f7ff, #e6f2ff);
            border-color: #007bff;
            box-shadow: 0 6px 12px rgba(0, 123, 255, 0.15);
        }

        .city-icon {
            width: 44px;
            height: 44px;
            background: linear-gradient(135deg, #007bff, #00c9a7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
            flex-shrink: 0;
        }

        .city-info {
            flex-grow: 1;
        }

        .city-name {
            font-weight: 600;
            color: #212529;
            margin-bottom: 0.3rem;
            font-size: 1.05rem;
        }

        .city-uni-count {
            font-size: 0.85rem;
            color: #6c757d;
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .city-uni-count::before {
            content: '🎓';
            font-size: 0.8rem;
        }

        /* Region content transition */
        #region-content {
            transition: opacity 0.3s ease;
        }

        /* Loading spinner styles */
        #region-loading {
            transition: all 0.3s ease;
        }

        .university-rank {
            position: absolute;
            top: 1rem;
            right: 1rem;
        }

        .rank-badge {
            background: linear-gradient(135deg, var(--accent), var(--accent-dark));
            color: var(--white);
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: 0.8rem;
            font-weight: 600;
        }

        .university-logo {
            display: flex;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .university-logo img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--primary-light);
        }

        .university-name {
            text-align: center;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--gray-900);
        }

        .university-details {
            margin-bottom: 1.2rem;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
        }

        .detail-item {
            display: flex;
            align-items: flex-start;
            gap: 0.6rem;
            margin-bottom: 0.7rem;
            color: #495057;
            font-size: 0.95rem;
        }

        .detail-item:last-child {
            margin-bottom: 0;
        }

        .detail-item i {
            margin-top: 0.2rem;
            color: #007bff;
        }

        .university-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.6rem;
            margin-bottom: 1.2rem;
        }

        .university-tag {
            display: inline-block;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #495057;
            border-radius: 20px;
            padding: 0.3rem 0.7rem;
            font-size: 0.8rem;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid #dee2e6;
        }

        /* Search and Filter Styles */
        .search-filter-container {
            margin-bottom: 1.5rem;
        }

        .search-bar {
            position: relative;
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-500);
            z-index: 1;
        }

        #university-search {
            padding-left: 40px;
            border-radius: var(--radius-full);
            border: 1px solid var(--gray-300);
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-normal) var(--transition-ease);
        }

        #university-search:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: var(--white);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            z-index: var(--z-dropdown);
            max-height: 300px;
            overflow-y: auto;
            display: none;
        }

        .search-suggestion-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background-color var(--transition-fast) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .search-suggestion-item:hover {
            background-color: var(--gray-100);
        }

        .suggestion-icon {
            color: var(--primary);
            font-size: 1rem;
            width: 20px;
            text-align: center;
        }

        .suggestion-text {
            flex: 1;
        }

        .suggestion-category {
            font-size: 0.75rem;
            color: var(--gray-500);
            background-color: var(--gray-100);
            padding: 0.2rem 0.5rem;
            border-radius: var(--radius-full);
        }

        /* Map View Styles */
        #map-view-container {
            margin-bottom: 2rem;
            display: none;
        }

        #university-map {
            height: 500px;
            width: 100%;
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
        }

        .map-popup {
            text-align: center;
            padding: 0.5rem;
        }

        .map-popup h5 {
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }

        .map-popup p {
            font-size: 0.85rem;
            margin-bottom: 0.5rem;
        }

        /* Comparison Tool Styles */
        #comparison-container {
            margin-bottom: 2rem;
            display: none;
        }

        .comparison-table {
            width: 100%;
            overflow-x: auto;
        }

        .comparison-table-content {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
        }

        .comparison-table-content th {
            background-color: #f8f9fa;
            padding: 1rem;
            text-align: left;
            position: sticky;
            left: 0;
            z-index: 1;
            min-width: 200px;
        }

        .comparison-table-content td {
            padding: 1rem;
            border-top: 1px solid #f0f0f0;
            min-width: 200px;
            text-align: center;
        }

        .comparison-uni-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: contain;
            border: 1px solid #f0f0f0;
            background-color: white;
            padding: 0.5rem;
            margin: 0 auto 0.5rem;
            display: block;
        }

        .btn-remove-comparison {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #dc3545;
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        /* Bookmarks Styles */
        #bookmarks-container {
            margin-bottom: 2rem;
            display: none;
        }

        .bookmark-count {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            background-color: #7F00FF;
            color: white;
            border-radius: 50%;
            font-size: 0.75rem;
            margin-left: 0.5rem;
        }

        .benefit-card {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: 1.5rem;
            text-align: center;
            transition: all var(--transition-normal) var(--transition-ease);
        }

        .benefit-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .benefit-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
            margin: 0 auto 1rem;
        }

        .benefit-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--gray-900);
        }

        .benefit-text {
            color: var(--gray-600);
            font-size: 0.9rem;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1.5rem;
        }

        .why-study-abroad {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: 2rem;
        }

        /* Default content styles */
        .default-content {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            box-shadow: 0 10px 20px rgba(0, 123, 255, 0.05);
            margin-bottom: 2rem;
        }

        .default-content-icon {
            margin-bottom: 1.5rem;
            color: #007bff;
        }

        .region-selection-help {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 2rem;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 1rem;
            background-color: #f8f9fa;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid #e9ecef;
            min-width: 220px;
        }

        .step-number {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #007bff, #00c9a7);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            box-shadow: 0 3px 6px rgba(0, 123, 255, 0.2);
        }

        .step-text {
            font-weight: 500;
            color: #495057;
        }

        /* Header Banner Styles - Sticky */
        .header-banner {
            background: linear-gradient(135deg, #0066CC 0%, #00c9a7 100%);
            padding: 10px 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            overflow: hidden;
            z-index: 1001;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .header-banner-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            margin: 0;
            padding: 0 20px;
        }

        /* Scholarship Banner */
        .scholarship-banner {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px 20px;
            border-radius: 25px;
            color: #0066CC;
            font-weight: 700;
            font-size: 1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .scholarship-text {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .scholarship-text i {
            color: #FFD700;
            font-size: 1.2rem;
        }

        .scholarship-cta {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        /* Country Rotator */
        /* Expanded Country Flags Section */
        .country-rotator {
            flex: 1;
            height: 70px;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 35px;
            overflow: hidden;
            margin-left: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        /* Horizontal Scrolling Countries */
        .countries-track {
            display: flex;
            height: 100%;
            animation: scrollCountries 60s linear infinite;
            gap: 25px;
            padding: 0 25px;
            align-items: center;
        }

        .country-item {
            display: flex;
            align-items: center;
            gap: 15px;
            min-width: 180px;
            height: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 10px 15px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
        }

        .country-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .country-flag-single {
            width: 60px;
            height: 45px;
            border-radius: 10px;
            object-fit: contain;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
            border: 3px solid rgba(255, 255, 255, 0.5);
            background: white;
            flex-shrink: 0;
        }

        .country-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
            flex: 1;
        }

        .country-code {
            color: white;
            font-weight: 900;
            font-size: 1.4rem;
            text-shadow: 0 2px 6px rgba(0, 0, 0, 0.7);
            line-height: 1;
            letter-spacing: 0.8px;
        }

        .country-full-name {
            color: rgba(255, 255, 255, 0.95);
            font-size: 1rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            line-height: 1;
        }

        /* Scrolling Animation */
        @keyframes scrollCountries {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        /* Pause animation on hover */
        .country-rotator:hover .countries-track {
            animation-play-state: paused;
        }

        /* Modern Logo Styles */
        .modern-logo {
            text-decoration: none !important;
            transition: all 0.3s ease;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            border-radius: 25px;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 201, 167, 0.1));
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .logo-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #007bff, #00c9a7);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .modern-logo:hover .logo-container {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
            border-color: rgba(0, 123, 255, 0.3);
        }

        .modern-logo:hover .logo-container::before {
            opacity: 0.1;
        }

        .globe-icon {
            position: relative;
            font-size: 1.8rem;
            color: #007bff;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 201, 167, 0.1));
        }

        .flight-path {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            border: 2px dashed rgba(0, 201, 167, 0.4);
            border-radius: 50%;
            opacity: 0;
            transition: all 0.3s ease;
            animation: rotatePath 8s linear infinite;
        }

        .modern-logo:hover .flight-path {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.2);
        }

        .modern-logo:hover .globe-icon {
            color: #00c9a7;
            transform: scale(1.1);
            animation: globePulse 2s ease-in-out infinite;
        }

        .logo-text {
            font-family: 'Inter', 'Poppins', 'Montserrat', sans-serif;
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #007bff, #00c9a7);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            letter-spacing: -0.5px;
        }

        .modern-logo:hover .logo-text {
            transform: translateX(3px);
            filter: drop-shadow(0 4px 8px rgba(0, 123, 255, 0.3));
        }

        @keyframes rotatePath {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        @keyframes globePulse {
            0%, 100% { transform: scale(1.1); }
            50% { transform: scale(1.2); }
        }
    </style>
</head>
<body>
    <!-- Header Banner -->
    <div class="header-banner">
        <div class="header-banner-content">
            <!-- Scholarship Banner -->
            <div class="scholarship-banner">
                <div class="scholarship-text">
                    <i class="fas fa-graduation-cap"></i>
                    <span>Scholarships Available – Apply Now!</span>
                </div>
                <a href="scholarships.html" class="scholarship-cta">Apply Now</a>
            </div>

            <!-- Single Rotating Country Display -->
            <!-- Horizontal Scrolling Country Flags -->
            <div class="country-rotator">
                <div class="countries-track" id="countriesTrack">
                    <!-- Countries will be dynamically inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation - Sticky Below Header -->
    <nav class="navbar navbar-expand-lg fixed-top bg-white" style="top: 90px; z-index: 1000; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px);">
        <div class="container">
            <a class="navbar-brand modern-logo" href="index.html">
                <div class="logo-container">
                    <div class="globe-icon">
                        <i class="fas fa-globe-americas"></i>
                        <div class="flight-path"></div>
                    </div>
                    <span class="logo-text">Go Abroad</span>
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="search_universities.html">Search Universities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="scholarships.html">Scholarships</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="applications.html">Applications</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admission_guide.html">Admission Guide</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <header class="page-header">
        <div class="container">
            <div class="page-header-content" data-aos="fade-up">
                <h1 class="page-title">Search Universities</h1>
                <p class="page-description">Explore universities by region and find your perfect study destination.</p>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <!-- Search and Filter Bar -->
            <div class="search-filter-container mb-4" data-aos="fade-up">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="search-bar">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" id="university-search" class="form-control" placeholder="Search universities, programs, or locations...">
                            <div id="search-suggestions" class="search-suggestions"></div>
                        </div>
                    </div>
                    <div class="col-md-6 d-flex justify-content-end">
                        <button class="btn btn-outline-primary me-2" id="filter-toggle">
                            <i class="fas fa-filter"></i> Filters
                        </button>
                        <button class="btn btn-outline-primary me-2" id="map-view-toggle">
                            <i class="fas fa-map-marked-alt"></i> Map View
                        </button>
                        <button class="btn btn-outline-primary me-2" id="comparison-toggle">
                            <i class="fas fa-exchange-alt"></i> Compare
                        </button>
                        <button class="btn btn-outline-primary" id="bookmarks-toggle">
                            <i class="fas fa-bookmark"></i> Bookmarks
                            <span class="bookmark-count">0</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Advanced Filters Panel (Hidden by default) -->
            <div id="advanced-filters" class="advanced-filters mb-4" style="display: none;" data-aos="fade-down">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title mb-3">Advanced Filters</h5>
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="field-filter" class="form-label">Field of Study</label>
                                <select id="field-filter" class="form-select">
                                    <option value="">All Fields</option>
                                    <option value="business">Business & Management</option>
                                    <option value="engineering">Engineering</option>
                                    <option value="computer-science">Computer Science</option>
                                    <option value="medicine">Medicine & Health</option>
                                    <option value="arts">Arts & Humanities</option>
                                    <option value="science">Science</option>
                                    <option value="social-sciences">Social Sciences</option>
                                    <option value="law">Law</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="tuition-filter" class="form-label">Tuition Range</label>
                                <select id="tuition-filter" class="form-select">
                                    <option value="">Any Range</option>
                                    <option value="low">Under $15,000/year</option>
                                    <option value="medium">$15,000 - $30,000/year</option>
                                    <option value="high">$30,000 - $50,000/year</option>
                                    <option value="very-high">Above $50,000/year</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="language-filter" class="form-label">Language Test</label>
                                <select id="language-filter" class="form-select">
                                    <option value="">Any Test</option>
                                    <option value="ielts">IELTS</option>
                                    <option value="toefl">TOEFL</option>
                                    <option value="duolingo">Duolingo</option>
                                    <option value="pte">PTE Academic</option>
                                    <option value="cambridge">Cambridge English</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="scholarship-filter" class="form-label">Scholarships</label>
                                <select id="scholarship-filter" class="form-select">
                                    <option value="">All Universities</option>
                                    <option value="available">Scholarships Available</option>
                                    <option value="full">Full Scholarships</option>
                                    <option value="partial">Partial Scholarships</option>
                                    <option value="merit">Merit-based</option>
                                    <option value="need">Need-based</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="rank-filter" class="form-label">QS Ranking</label>
                                <select id="rank-filter" class="form-select">
                                    <option value="">All Rankings</option>
                                    <option value="top50">Top 50</option>
                                    <option value="top100">Top 100</option>
                                    <option value="top200">Top 200</option>
                                    <option value="top500">Top 500</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="gpa-filter" class="form-label">GPA Requirements</label>
                                <select id="gpa-filter" class="form-select">
                                    <option value="">All GPA Requirements</option>
                                    <option value="low">Low (Below 3.0)</option>
                                    <option value="medium">Medium (3.0 - 3.5)</option>
                                    <option value="high">High (Above 3.5)</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end mt-3">
                            <button id="reset-filters" class="btn btn-outline-secondary me-2">Reset Filters</button>
                            <button id="apply-filters" class="btn btn-primary">Apply Filters</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-3" data-aos="fade-up">
                <ol class="breadcrumb" id="navigation-breadcrumb">
                    <li class="breadcrumb-item active">Select a Region</li>
                </ol>
            </nav>

            <!-- Region Tabs -->
            <div class="region-tabs-container mb-4" data-aos="fade-up" data-aos-offset="-100">
                <div class="region-tabs-wrapper">
                    <div class="region-tabs">
                        <button class="region-tab active" data-region="uk">
                            <span class="region-flag">🇬🇧</span> UK
                        </button>
                        <button class="region-tab" data-region="europe">
                            <span class="region-flag">🇪🇺</span> Europe
                        </button>
                        <button class="region-tab" data-region="usa">
                            <span class="region-flag">🇺🇸</span> USA
                        </button>
                        <button class="region-tab" data-region="canada">
                            <span class="region-flag">🇨🇦</span> Canada
                        </button>
                        <button class="region-tab" data-region="australia">
                            <span class="region-flag">🇦🇺</span> Australia & NZ
                        </button>
                        <button class="region-tab" data-region="asia">
                            <span class="region-flag">🇯🇵</span> Asia
                        </button>
                        <button class="region-tab" data-region="middleeast">
                            <span class="region-flag">🇦🇪</span> Middle East
                        </button>
                        <button class="region-tab" data-region="africa">
                            <span class="region-flag">🇿🇦</span> Africa
                        </button>
                        <button class="region-tab" data-region="southamerica">
                            <span class="region-flag">🇧🇷</span> South America
                        </button>
                    </div>
                    <div class="region-tabs-nav">
                        <button class="region-tabs-nav-btn prev" aria-label="Previous regions">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="region-tabs-nav-btn next" aria-label="Next regions">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Map View Container (Hidden by default) -->
            <div id="map-view-container" class="map-view-container mb-4" style="display: none;" data-aos="fade-up">
                <div class="card">
                    <div class="card-body p-0">
                        <div id="university-map" class="university-map">
                            <!-- Map will be loaded here -->
                            <div class="map-placeholder d-flex align-items-center justify-content-center">
                                <div class="text-center">
                                    <i class="fas fa-map-marked-alt fa-4x text-primary mb-3"></i>
                                    <h4>Map View</h4>
                                    <p>Select a region to view universities on the map</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- University Comparison Tool (Hidden by default) -->
            <div id="comparison-container" class="comparison-container mb-4" style="display: none;" data-aos="fade-up">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title mb-3">University Comparison</h5>
                        <p class="text-muted mb-3">Add universities to compare their features side by side</p>
                        <div id="comparison-table" class="comparison-table">
                            <div class="text-center py-5">
                                <i class="fas fa-exchange-alt fa-3x text-primary mb-3"></i>
                                <h5>No Universities Selected</h5>
                                <p>Click the "Add to Compare" button on university cards to add them here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bookmarks Container (Hidden by default) -->
            <div id="bookmarks-container" class="bookmarks-container mb-4" style="display: none;" data-aos="fade-up">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title mb-3">Your Bookmarked Universities</h5>
                        <div id="bookmarked-universities" class="universities-grid">
                            <div class="text-center py-5">
                                <i class="fas fa-bookmark fa-3x text-primary mb-3"></i>
                                <h5>No Bookmarks Yet</h5>
                                <p>Click the bookmark icon on university cards to save them here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Country Tabs Container (Hidden by default) -->
            <div id="country-tabs-container" class="country-tabs-container mb-4" style="display: none;" data-aos="fade-up">
                <div class="country-tabs-wrapper">
                    <div id="country-tabs" class="country-tabs">
                        <!-- Country tabs will be dynamically populated by JavaScript -->
                    </div>
                    <div class="country-tabs-nav">
                        <button class="country-tabs-nav-btn prev" aria-label="Previous countries">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="country-tabs-nav-btn next" aria-label="Next countries">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Region Content Container -->
            <div id="region-content-container" class="mb-5" data-aos="fade-up">
                <!-- Loading Spinner -->
                <div id="region-loading" class="text-center py-5" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading universities...</p>
                </div>

                <!-- Dynamic Region Content -->
                <div id="region-content" class="region-content">
                    <!-- Content will be dynamically populated by JavaScript -->
                </div>
            </div>

            <!-- Why Study Abroad Section -->
            <section class="why-study-abroad mt-5" data-aos="fade-up">
                <div class="section-header">
                    <h2 class="section-title">Why Study Abroad?</h2>
                    <p class="section-subtitle">Studying abroad offers numerous benefits for your academic and personal growth</p>
                </div>

                <div class="benefits-grid mt-4">
                    <div class="benefit-card" data-aos="fade-up" data-aos-delay="100">
                        <div class="benefit-icon">
                            <i class="fas fa-globe-americas"></i>
                        </div>
                        <h3 class="benefit-title">Global Perspective</h3>
                        <p class="benefit-text">Gain a broader worldview and cultural understanding through international exposure.</p>
                    </div>

                    <div class="benefit-card" data-aos="fade-up" data-aos-delay="200">
                        <div class="benefit-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <h3 class="benefit-title">Career Opportunities</h3>
                        <p class="benefit-text">Enhance your resume and access international job markets with global experience.</p>
                    </div>

                    <div class="benefit-card" data-aos="fade-up" data-aos-delay="300">
                        <div class="benefit-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h3 class="benefit-title">Language Skills</h3>
                        <p class="benefit-text">Improve language proficiency through immersion in a native-speaking environment.</p>
                    </div>

                    <div class="benefit-card" data-aos="fade-up" data-aos-delay="400">
                        <div class="benefit-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h3 class="benefit-title">Quality Education</h3>
                        <p class="benefit-text">Access world-class teaching and research facilities at top global institutions.</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="footer-brand">
                        <i class="fas fa-globe-americas"></i> Go Abroad
                    </div>
                    <p class="footer-text">Your trusted partner for international education. We help students achieve their dreams of studying abroad.</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Quick Links</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="index.html">Home</a></li>
                        <li class="footer-link"><a href="search_universities.html">Universities</a></li>
                        <li class="footer-link"><a href="scholarships.html">Scholarships</a></li>
                        <li class="footer-link"><a href="applications.html">Applications</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Destinations</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="#">United Kingdom</a></li>
                        <li class="footer-link"><a href="#">United States</a></li>
                        <li class="footer-link"><a href="#">Canada</a></li>
                        <li class="footer-link"><a href="#">Australia</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Contact</h3>
                    <ul class="footer-links">
                        <li class="footer-contact"><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li class="footer-contact"><i class="fas fa-phone me-2"></i> +****************</li>
                        <li class="footer-contact"><i class="fas fa-map-marker-alt me-2"></i> New York, NY</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Go Abroad. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Leaflet JS for Map View -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <script>
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Horizontal Scrolling Countries Functionality
        function initializeCountryRotator() {
            const countries = [
                // Europe
                { code: 'UK', name: 'United Kingdom', flag: 'https://flagcdn.com/w320/gb.png', region: 'europe' },
                { code: 'GER', name: 'Germany', flag: 'https://flagcdn.com/w320/de.png', region: 'europe' },
                { code: 'FRA', name: 'France', flag: 'https://flagcdn.com/w320/fr.png', region: 'europe' },
                { code: 'ITA', name: 'Italy', flag: 'https://flagcdn.com/w320/it.png', region: 'europe' },
                { code: 'ESP', name: 'Spain', flag: 'https://flagcdn.com/w320/es.png', region: 'europe' },
                { code: 'NLD', name: 'Netherlands', flag: 'https://flagcdn.com/w320/nl.png', region: 'europe' },
                { code: 'SWE', name: 'Sweden', flag: 'https://flagcdn.com/w320/se.png', region: 'europe' },
                { code: 'CHE', name: 'Switzerland', flag: 'https://flagcdn.com/w320/ch.png', region: 'europe' },
                { code: 'NOR', name: 'Norway', flag: 'https://flagcdn.com/w320/no.png', region: 'europe' },
                { code: 'DNK', name: 'Denmark', flag: 'https://flagcdn.com/w320/dk.png', region: 'europe' },
                { code: 'FIN', name: 'Finland', flag: 'https://flagcdn.com/w320/fi.png', region: 'europe' },
                { code: 'AUT', name: 'Austria', flag: 'https://flagcdn.com/w320/at.png', region: 'europe' },
                { code: 'BEL', name: 'Belgium', flag: 'https://flagcdn.com/w320/be.png', region: 'europe' },
                { code: 'IRL', name: 'Ireland', flag: 'https://flagcdn.com/w320/ie.png', region: 'europe' },
                { code: 'POL', name: 'Poland', flag: 'https://flagcdn.com/w320/pl.png', region: 'europe' },
                { code: 'PRT', name: 'Portugal', flag: 'https://flagcdn.com/w320/pt.png', region: 'europe' },
                { code: 'GRC', name: 'Greece', flag: 'https://flagcdn.com/w320/gr.png', region: 'europe' },
                { code: 'CZE', name: 'Czech Republic', flag: 'https://flagcdn.com/w320/cz.png', region: 'europe' },
                { code: 'HUN', name: 'Hungary', flag: 'https://flagcdn.com/w320/hu.png', region: 'europe' },

                // North America
                { code: 'USA', name: 'United States', flag: 'https://flagcdn.com/w320/us.png', region: 'usa' },
                { code: 'CAN', name: 'Canada', flag: 'https://flagcdn.com/w320/ca.png', region: 'canada' },

                // Oceania
                { code: 'AUS', name: 'Australia', flag: 'https://flagcdn.com/w320/au.png', region: 'australia' },
                { code: 'NZL', name: 'New Zealand', flag: 'https://flagcdn.com/w320/nz.png', region: 'australia' },

                // Asia
                { code: 'JPN', name: 'Japan', flag: 'https://flagcdn.com/w320/jp.png', region: 'asia' },
                { code: 'KOR', name: 'South Korea', flag: 'https://flagcdn.com/w320/kr.png', region: 'asia' },
                { code: 'SGP', name: 'Singapore', flag: 'https://flagcdn.com/w320/sg.png', region: 'asia' },
                { code: 'HKG', name: 'Hong Kong', flag: 'https://flagcdn.com/w320/hk.png', region: 'asia' },
                { code: 'CHN', name: 'China', flag: 'https://flagcdn.com/w320/cn.png', region: 'asia' },
                { code: 'MYS', name: 'Malaysia', flag: 'https://flagcdn.com/w320/my.png', region: 'asia' },
                { code: 'THA', name: 'Thailand', flag: 'https://flagcdn.com/w320/th.png', region: 'asia' },

                // Middle East
                { code: 'UAE', name: 'United Arab Emirates', flag: 'https://flagcdn.com/w320/ae.png', region: 'middle-east' },
                { code: 'QAT', name: 'Qatar', flag: 'https://flagcdn.com/w320/qa.png', region: 'middle-east' },
                { code: 'SAU', name: 'Saudi Arabia', flag: 'https://flagcdn.com/w320/sa.png', region: 'middle-east' },
                { code: 'TUR', name: 'Turkey', flag: 'https://flagcdn.com/w320/tr.png', region: 'middle-east' },

                // South America
                { code: 'BRA', name: 'Brazil', flag: 'https://flagcdn.com/w320/br.png', region: 'south-america' },
                { code: 'ARG', name: 'Argentina', flag: 'https://flagcdn.com/w320/ar.png', region: 'south-america' },
                { code: 'CHL', name: 'Chile', flag: 'https://flagcdn.com/w320/cl.png', region: 'south-america' },

                // Africa
                { code: 'ZAF', name: 'South Africa', flag: 'https://flagcdn.com/w320/za.png', region: 'africa' },
                { code: 'EGY', name: 'Egypt', flag: 'https://flagcdn.com/w320/eg.png', region: 'africa' },
                { code: 'MAR', name: 'Morocco', flag: 'https://flagcdn.com/w320/ma.png', region: 'africa' }
            ];

            const track = document.getElementById('countriesTrack');
            if (!track) return;

            // Create countries twice for seamless loop
            const allCountries = [...countries, ...countries];

            allCountries.forEach((country, index) => {
                const countryItem = document.createElement('div');
                countryItem.className = 'country-item';

                countryItem.innerHTML = `
                    <img src="${country.flag}" alt="${country.name} Flag" class="country-flag-single">
                    <div class="country-info">
                        <div class="country-code">${country.code}</div>
                        <div class="country-full-name">${country.name}</div>
                    </div>
                `;

                // Add click handler for navigation
                countryItem.addEventListener('click', () => {
                    window.location.href = `search_universities.html?country=${country.code.toLowerCase()}`;
                });

                track.appendChild(countryItem);
            });

            // Calculate total width for animation
            const itemWidth = 180 + 25; // min-width + gap
            const totalWidth = allCountries.length * itemWidth;
            track.style.width = totalWidth + 'px';
        }

        // Initialize country rotator
        initializeCountryRotator();

        // Region content data
        const regionContent = {
            'uk': {
                title: '🇬🇧 United Kingdom',
                description: 'The United Kingdom offers world-class education with globally recognized universities and diverse program options.',
                countries: [
                    {
                        name: '🏴󠁧󠁢󠁥󠁮󠁧󠁿 England',
                        flag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
                        cities: ['London', 'Manchester', 'Birmingham', 'Oxford', 'Cambridge', 'Leeds', 'Bristol', 'Sheffield'],
                        universities: {
                            'London': [
                                {
                                    name: 'University College London',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/9a/University_College_London_logo.svg/200px-University_College_London_logo.svg.png',
                                    rank: 8,
                                    programs: 'Computer Science, Medicine, Law',
                                    tuition: '£23,000 - £35,000 per year',
                                    deadline: 'January 15, 2025',
                                    tags: ['IELTS: 6.5+', 'Scholarships'],
                                    requirements: {
                                        english: 'IELTS: 6.5+ (min 6.0 in each component)',
                                        gre: 'Not required for most programs',
                                        gpa: '3.3/4.0 or equivalent',
                                        other: 'Portfolio required for some arts programs'
                                    },
                                    location: {
                                        lat: 51.5246,
                                        lng: -0.1339
                                    }
                                },
                                {
                                    name: 'Imperial College London',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/ea/Imperial_College_London_crest.svg/200px-Imperial_College_London_crest.svg.png',
                                    rank: 7,
                                    programs: 'Engineering, Medicine, Science',
                                    tuition: '£25,000 - £38,000 per year',
                                    deadline: 'February 28, 2025',
                                    tags: ['IELTS: 7.0+', 'GRE Required'],
                                    requirements: {
                                        english: 'IELTS: 7.0+ (min 6.5 in each component)',
                                        gre: 'Required for most Engineering and Science programs',
                                        gpa: '3.5/4.0 or equivalent',
                                        other: 'Research proposal for PhD applicants'
                                    },
                                    location: {
                                        lat: 51.4988,
                                        lng: -0.1749
                                    }
                                },
                                {
                                    name: 'London School of Economics',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/22/LSE_Logo.svg/200px-LSE_Logo.svg.png',
                                    rank: 49,
                                    programs: 'Economics, Political Science, Law',
                                    tuition: '£22,000 - £32,000 per year',
                                    deadline: 'March 15, 2025',
                                    tags: ['IELTS: 7.0+', 'GRE for Economics'],
                                    requirements: {
                                        english: 'IELTS: 7.0+ (min 6.5 in each component)',
                                        gre: 'Required for Economics programs',
                                        gpa: '3.5/4.0 or equivalent',
                                        other: 'Work experience preferred for management programs'
                                    },
                                    location: {
                                        lat: 51.5144,
                                        lng: -0.1165
                                    }
                                }
                            ],
                            'Oxford': [
                                {
                                    name: 'University of Oxford',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/ff/Oxford-University-Circlet.svg/200px-Oxford-University-Circlet.svg.png',
                                    rank: 2,
                                    programs: 'Humanities, Sciences, Social Sciences',
                                    tuition: '£26,000 - £37,000 per year',
                                    deadline: 'January 15, 2025',
                                    tags: ['IELTS: 7.0+', 'Scholarships'],
                                    requirements: {
                                        english: 'IELTS: 7.0+ (min 6.5 in each component)',
                                        gre: 'Not required for most programs',
                                        gpa: '3.7/4.0 or equivalent',
                                        other: 'Written work samples for humanities'
                                    },
                                    location: {
                                        lat: 51.7548,
                                        lng: -1.2544
                                    }
                                }
                            ],
                            'Cambridge': [
                                {
                                    name: 'University of Cambridge',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c9/University_of_Cambridge_coat_of_arms.svg/200px-University_of_Cambridge_coat_of_arms.svg.png',
                                    rank: 3,
                                    programs: 'Natural Sciences, Engineering, Humanities',
                                    tuition: '£27,000 - £38,000 per year',
                                    deadline: 'January 15, 2025',
                                    tags: ['IELTS: 7.0+', 'Scholarships'],
                                    requirements: {
                                        english: 'IELTS: 7.0+ (min 7.0 in each component)',
                                        gre: 'Not required for most programs',
                                        gpa: '3.7/4.0 or equivalent',
                                        other: 'College-specific requirements may apply'
                                    },
                                    location: {
                                        lat: 52.2053,
                                        lng: 0.1218
                                    }
                                }
                            ],
                            'Manchester': [
                                {
                                    name: 'University of Manchester',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c9/University_of_Manchester_coat_of_arms.svg/200px-University_of_Manchester_coat_of_arms.svg.png',
                                    rank: 27,
                                    programs: 'Engineering, Business, Life Sciences',
                                    tuition: '£20,000 - £30,000 per year',
                                    deadline: 'April 30, 2025',
                                    tags: ['IELTS: 6.5+', 'Scholarships Available'],
                                    requirements: {
                                        english: 'IELTS: 6.5+ (min 6.0 in each component)',
                                        gre: 'Not required for most programs',
                                        gpa: '3.0/4.0 or equivalent',
                                        other: 'Portfolio for creative arts programs'
                                    },
                                    location: {
                                        lat: 53.4668,
                                        lng: -2.2339
                                    }
                                }
                            ]
                        }
                    },
                    {
                        name: '🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scotland',
                        flag: '🏴󠁧󠁢󠁳󠁣󠁴󠁿',
                        cities: ['Edinburgh', 'Glasgow', 'Aberdeen', 'St Andrews', 'Dundee'],
                        universities: {
                            'Edinburgh': [
                                {
                                    name: 'University of Edinburgh',
                                    logo: 'https://upload.wikimedia.org/wikipedia/en/thumb/f/f1/University_of_Edinburgh_ceremonial_crest.svg/200px-University_of_Edinburgh_ceremonial_crest.svg.png',
                                    rank: 15,
                                    programs: 'Computer Science, Medicine, Arts',
                                    tuition: '£26,000 - £37,000 per year',
                                    deadline: 'May 30, 2025',
                                    tags: ['IELTS: 6.5+', 'PTE: 62+'],
                                    requirements: {
                                        english: 'IELTS: 6.5+ (min 6.0 in each component)',
                                        gre: 'Not required for most programs',
                                        gpa: '3.3/4.0 or equivalent',
                                        other: 'Portfolio for design and architecture'
                                    },
                                    location: {
                                        lat: 55.9445,
                                        lng: -3.1892
                                    }
                                }
                            ],
                            'Glasgow': [
                                {
                                    name: 'University of Glasgow',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/a9/University_of_Glasgow_coat_of_arms.svg/200px-University_of_Glasgow_coat_of_arms.svg.png',
                                    rank: 73,
                                    programs: 'Medicine, Veterinary Science, Arts',
                                    tuition: '£24,000 - £35,000 per year',
                                    deadline: 'June 30, 2025',
                                    tags: ['IELTS: 6.5+', 'PTE: 60+'],
                                    requirements: {
                                        english: 'IELTS: 6.5+ (min 6.0 in each component)',
                                        gre: 'Not required for most programs',
                                        gpa: '3.0/4.0 or equivalent',
                                        other: 'Additional requirements for Medicine and Veterinary Science'
                                    },
                                    location: {
                                        lat: 55.8724,
                                        lng: -4.2900
                                    }
                                }
                            ],
                            'St Andrews': [
                                {
                                    name: 'University of St Andrews',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1a/Crest_of_the_University_of_St_Andrews.svg/200px-Crest_of_the_University_of_St_Andrews.svg.png',
                                    rank: 91,
                                    programs: 'Arts, Sciences, International Relations',
                                    tuition: '£25,000 - £33,000 per year',
                                    deadline: 'May 15, 2025',
                                    tags: ['IELTS: 7.0+', 'Scholarships'],
                                    requirements: {
                                        english: 'IELTS: 7.0+ (min 6.5 in each component)',
                                        gre: 'Not required for most programs',
                                        gpa: '3.5/4.0 or equivalent',
                                        other: 'Personal statement highly important'
                                    },
                                    location: {
                                        lat: 56.3417,
                                        lng: -2.7943
                                    }
                                }
                            ]
                        }
                    },
                    {
                        name: '🏴󠁧󠁢󠁷󠁬󠁳󠁿 Wales',
                        flag: '🏴󠁧󠁢󠁷󠁬󠁳󠁿',
                        cities: ['Cardiff', 'Swansea', 'Aberystwyth', 'Bangor'],
                        universities: {
                            'Cardiff': [
                                {
                                    name: 'Cardiff University',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/7e/Cardiff-university-vector-logo.svg/200px-Cardiff-university-vector-logo.svg.png',
                                    rank: 151,
                                    programs: 'Medicine, Engineering, Business',
                                    tuition: '£19,000 - £29,000 per year',
                                    deadline: 'July 15, 2025',
                                    tags: ['IELTS: 6.5+', 'Scholarships Available'],
                                    requirements: {
                                        english: 'IELTS: 6.5+ (min 6.0 in each component)',
                                        gre: 'Not required for most programs',
                                        gpa: '3.0/4.0 or equivalent',
                                        other: 'Interviews for Medicine and Dentistry'
                                    },
                                    location: {
                                        lat: 51.4866,
                                        lng: -3.1790
                                    }
                                }
                            ],
                            'Swansea': [
                                {
                                    name: 'Swansea University',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/71/Swansea_University_logo.svg/200px-Swansea_University_logo.svg.png',
                                    rank: 440,
                                    programs: 'Engineering, Medicine, Business',
                                    tuition: '£17,000 - £25,000 per year',
                                    deadline: 'August 1, 2025',
                                    tags: ['IELTS: 6.0+', 'Affordable Tuition'],
                                    requirements: {
                                        english: 'IELTS: 6.0+ (min 5.5 in each component)',
                                        gre: 'Not required',
                                        gpa: '2.8/4.0 or equivalent',
                                        other: 'Work experience valued for MBA'
                                    },
                                    location: {
                                        lat: 51.6092,
                                        lng: -3.9802
                                    }
                                }
                            ]
                        }
                    },
                    {
                        name: '🇬🇧 Northern Ireland',
                        flag: '🇬🇧',
                        cities: ['Belfast', 'Londonderry', 'Coleraine'],
                        universities: {
                            'Belfast': [
                                {
                                    name: 'Queen\'s University Belfast',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/90/Queens_University_Belfast_Crest.svg/200px-Queens_University_Belfast_Crest.svg.png',
                                    rank: 209,
                                    programs: 'Law, Medicine, Engineering',
                                    tuition: '£18,000 - £27,000 per year',
                                    deadline: 'June 30, 2025',
                                    tags: ['IELTS: 6.5+', 'Affordable Tuition'],
                                    requirements: {
                                        english: 'IELTS: 6.5+ (min 5.5 in each component)',
                                        gre: 'Not required for most programs',
                                        gpa: '3.0/4.0 or equivalent',
                                        other: 'Interviews for Medicine and Dentistry'
                                    },
                                    location: {
                                        lat: 54.5844,
                                        lng: -5.9342
                                    }
                                }
                            ],
                            'Coleraine': [
                                {
                                    name: 'Ulster University',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f2/Ulster_University_Logo.svg/200px-Ulster_University_Logo.svg.png',
                                    rank: 601,
                                    programs: 'Business, Computing, Arts',
                                    tuition: '£15,000 - £22,000 per year',
                                    deadline: 'July 31, 2025',
                                    tags: ['IELTS: 6.0+', 'Affordable'],
                                    requirements: {
                                        english: 'IELTS: 6.0+ (min 5.5 in each component)',
                                        gre: 'Not required',
                                        gpa: '2.8/4.0 or equivalent',
                                        other: 'Portfolio for art and design courses'
                                    },
                                    location: {
                                        lat: 55.1498,
                                        lng: -6.6752
                                    }
                                }
                            ]
                        }
                    }
                ]
            },
            'europe': {
                title: '🇪🇺 Europe',
                description: 'Europe offers diverse educational opportunities with many programs taught in English and affordable tuition options.',
                countries: [
                    {
                        name: '🇩🇪 Germany',
                        flag: '🇩🇪',
                        cities: ['Berlin', 'Munich', 'Heidelberg', 'Frankfurt', 'Hamburg'],
                        universities: {
                            'Munich': [
                                {
                                    name: 'Technical University of Munich',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c8/Logo_of_the_Technical_University_of_Munich.svg/200px-Logo_of_the_Technical_University_of_Munich.svg.png',
                                    rank: 50,
                                    programs: 'Engineering, Computer Science, Natural Sciences',
                                    tuition: 'No tuition (Admin fee: €150/semester)',
                                    deadline: 'January 15, 2025',
                                    tags: ['German: B2', 'IELTS: 6.5+'],
                                    requirements: {
                                        english: 'IELTS: 6.5+ or equivalent',
                                        german: 'B2 level for German-taught programs',
                                        gre: 'Required for some Master\'s programs',
                                        gpa: 'Good to very good in previous studies',
                                        other: 'Motivation letter and CV required'
                                    },
                                    location: {
                                        lat: 48.1496,
                                        lng: 11.5678
                                    }
                                }
                            ],
                            'Heidelberg': [
                                {
                                    name: 'Heidelberg University',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/16/Ruprecht-Karls-Universit%C3%A4t_Heidelberg_seal.svg/200px-Ruprecht-Karls-Universit%C3%A4t_Heidelberg_seal.svg.png',
                                    rank: 64,
                                    programs: 'Medicine, Physics, Humanities',
                                    tuition: 'No tuition (Admin fee: €170/semester)',
                                    deadline: 'July 15, 2025',
                                    tags: ['German: C1', 'IELTS: 7.0+'],
                                    requirements: {
                                        english: 'IELTS: 7.0+ for English programs',
                                        german: 'C1 level for German-taught programs',
                                        gre: 'Not required for most programs',
                                        gpa: 'Above average in previous studies',
                                        other: 'Research proposal for PhD applicants'
                                    },
                                    location: {
                                        lat: 49.3988,
                                        lng: 8.6724
                                    }
                                }
                            ],
                            'Berlin': [
                                {
                                    name: 'Humboldt University of Berlin',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/44/Humboldt-Universit%C3%A4t_zu_Berlin_logo.svg/200px-Humboldt-Universit%C3%A4t_zu_Berlin_logo.svg.png',
                                    rank: 117,
                                    programs: 'Humanities, Social Sciences, Law',
                                    tuition: 'No tuition (Admin fee: €300/semester)',
                                    deadline: 'July 15, 2025',
                                    tags: ['German: B2', 'IELTS: 6.5+'],
                                    requirements: {
                                        english: 'IELTS: 6.5+ for English programs',
                                        german: 'B2 level for German-taught programs',
                                        gre: 'Not required for most programs',
                                        gpa: 'Above average in previous studies',
                                        other: 'Motivation letter required'
                                    },
                                    location: {
                                        lat: 52.5200,
                                        lng: 13.3936
                                    }
                                },
                                {
                                    name: 'Free University of Berlin',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f1/FU-Berlin-Logo.svg/200px-FU-Berlin-Logo.svg.png',
                                    rank: 130,
                                    programs: 'Political Science, Humanities, Natural Sciences',
                                    tuition: 'No tuition (Admin fee: €300/semester)',
                                    deadline: 'June 15, 2025',
                                    tags: ['German: B2', 'IELTS: 6.5+'],
                                    requirements: {
                                        english: 'IELTS: 6.5+ for English programs',
                                        german: 'B2 level for German-taught programs',
                                        gre: 'Not required for most programs',
                                        gpa: 'Above average in previous studies',
                                        other: 'Motivation letter required'
                                    },
                                    location: {
                                        lat: 52.4536,
                                        lng: 13.2862
                                    }
                                }
                            ]
                        }
                    },
                    {
                        name: '🇫🇷 France',
                        flag: '🇫🇷',
                        cities: ['Paris', 'Lyon', 'Marseille', 'Toulouse', 'Bordeaux'],
                        universities: {
                            'Paris': [
                                {
                                    name: 'Sorbonne University',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/0a/Sorbonne_Universit%C3%A9_Logo.svg/200px-Sorbonne_Universit%C3%A9_Logo.svg.png',
                                    rank: 83,
                                    programs: 'Arts, Sciences, Medicine',
                                    tuition: '€3,770/year',
                                    deadline: 'March 15, 2025',
                                    tags: ['French: B2', 'IELTS: 6.5+'],
                                    requirements: {
                                        english: 'IELTS: 6.5+ for English programs',
                                        french: 'B2 level for French-taught programs',
                                        gre: 'Not required for most programs',
                                        gpa: 'Above average in previous studies',
                                        other: 'Motivation letter required'
                                    },
                                    location: {
                                        lat: 48.8496,
                                        lng: 2.3396
                                    }
                                },
                                {
                                    name: 'Sciences Po',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/a3/Sciences_Po_2022_logo.svg/200px-Sciences_Po_2022_logo.svg.png',
                                    rank: 242,
                                    programs: 'Political Science, Economics, Law',
                                    tuition: '€14,500/year',
                                    deadline: 'February 1, 2025',
                                    tags: ['French: B2', 'English: C1'],
                                    requirements: {
                                        english: 'C1 level for English programs',
                                        french: 'B2 level for French-taught programs',
                                        gre: 'Not required for most programs',
                                        gpa: 'Above average in previous studies',
                                        other: 'Motivation letter and CV required'
                                    },
                                    location: {
                                        lat: 48.8539,
                                        lng: 2.3309
                                    }
                                }
                            ],
                            'Lyon': [
                                {
                                    name: 'University of Lyon',
                                    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e8/Universit%C3%A9_de_Lyon_logo.svg/200px-Universit%C3%A9_de_Lyon_logo.svg.png',
                                    rank: 301,
                                    programs: 'Sciences, Humanities, Business',
                                    tuition: '€3,770/year',
                                    deadline: 'April 15, 2025',
                                    tags: ['French: B2', 'IELTS: 6.5+'],
                                    requirements: {
                                        english: 'IELTS: 6.5+ for English programs',
                                        french: 'B2 level for French-taught programs',
                                        gre: 'Not required for most programs',
                                        gpa: 'Above average in previous studies',
                                        other: 'Motivation letter required'
                                    },
                                    location: {
                                        lat: 45.7578,
                                        lng: 4.8320
                                    }
                                }
                            ]
                        }
                    },
                    {
                        name: '🇮🇹 Italy',
                        flag: '🇮🇹',
                        cities: ['Rome', 'Milan', 'Bologna', 'Florence', 'Turin'],
                        universities: {
                            'Milan': [
                                {
                                    name: 'Politecnico di Milano',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=PM',
                                    rank: 142,
                                    programs: 'Engineering, Architecture, Design',
                                    tuition: '€3,900/year',
                                    deadline: 'April 30, 2025',
                                    tags: ['Italian: B2', 'IELTS: 6.0+']
                                }
                            ],
                            'Bologna': [
                                {
                                    name: 'University of Bologna',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UB',
                                    rank: 167,
                                    programs: 'Humanities, Law, Economics',
                                    tuition: '€2,200/year',
                                    deadline: 'May 15, 2025',
                                    tags: ['Italian: B2', 'English: B2']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇳🇱 Netherlands',
                        flag: '🇳🇱',
                        cities: ['Amsterdam', 'Rotterdam', 'Utrecht', 'Leiden', 'Groningen'],
                        universities: {
                            'Amsterdam': [
                                {
                                    name: 'University of Amsterdam',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UvA',
                                    rank: 55,
                                    programs: 'Social Sciences, Economics, Humanities',
                                    tuition: '€8,000 - €15,000/year',
                                    deadline: 'April 1, 2025',
                                    tags: ['IELTS: 6.5+', 'English-taught programs']
                                }
                            ],
                            'Rotterdam': [
                                {
                                    name: 'Erasmus University Rotterdam',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=EUR',
                                    rank: 72,
                                    programs: 'Business, Economics, Medicine',
                                    tuition: '€8,500 - €15,000/year',
                                    deadline: 'May 1, 2025',
                                    tags: ['IELTS: 6.5+', 'GMAT for Business']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇪🇸 Spain',
                        flag: '🇪🇸',
                        cities: ['Madrid', 'Barcelona', 'Valencia', 'Seville', 'Granada'],
                        universities: {
                            'Barcelona': [
                                {
                                    name: 'University of Barcelona',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UB',
                                    rank: 183,
                                    programs: 'Medicine, Economics, Law',
                                    tuition: '€2,500 - €4,000/year',
                                    deadline: 'June 30, 2025',
                                    tags: ['Spanish: B2', 'IELTS: 6.0+']
                                }
                            ],
                            'Madrid': [
                                {
                                    name: 'Complutense University of Madrid',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UCM',
                                    rank: 233,
                                    programs: 'Humanities, Social Sciences, Sciences',
                                    tuition: '€2,000 - €3,500/year',
                                    deadline: 'July 15, 2025',
                                    tags: ['Spanish: B2', 'Limited English programs']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇸🇪 Sweden',
                        flag: '🇸🇪',
                        cities: ['Stockholm', 'Uppsala', 'Lund', 'Gothenburg', 'Malmö'],
                        universities: {
                            'Stockholm': [
                                {
                                    name: 'KTH Royal Institute of Technology',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=KTH',
                                    rank: 98,
                                    programs: 'Engineering, Computer Science, Architecture',
                                    tuition: '€15,000/year (non-EU)',
                                    deadline: 'January 15, 2025',
                                    tags: ['IELTS: 6.5+', 'English-taught programs']
                                }
                            ],
                            'Lund': [
                                {
                                    name: 'Lund University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=LU',
                                    rank: 95,
                                    programs: 'Engineering, Sciences, Humanities',
                                    tuition: '€13,000 - €15,000/year (non-EU)',
                                    deadline: 'January 15, 2025',
                                    tags: ['IELTS: 6.5+', 'English-taught programs']
                                }
                            ]
                        }
                    }
                ]
            },
            'usa': {
                title: '🇺🇸 United States',
                description: 'The United States hosts many of the world\'s top-ranked universities with extensive research opportunities and diverse campus experiences.',
                countries: [
                    {
                        name: 'Northeast',
                        flag: '🇺🇸',
                        cities: ['Boston', 'New York', 'Philadelphia', 'New Haven'],
                        universities: {
                            'Boston': [
                                {
                                    name: 'Harvard University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=HU',
                                    rank: 5,
                                    programs: 'Business, Law, Medicine',
                                    tuition: '$51,000 - $55,000 per year',
                                    deadline: 'December 15, 2024',
                                    tags: ['TOEFL: 100+', 'GRE/GMAT Required']
                                },
                                {
                                    name: 'MIT',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=MIT',
                                    rank: 1,
                                    programs: 'Engineering, Computer Science, Business',
                                    tuition: '$53,000 - $57,000 per year',
                                    deadline: 'December 15, 2024',
                                    tags: ['TOEFL: 100+', 'GRE Required']
                                }
                            ],
                            'New York': [
                                {
                                    name: 'Columbia University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=CU',
                                    rank: 11,
                                    programs: 'Journalism, Business, Arts',
                                    tuition: '$50,000 - $60,000 per year',
                                    deadline: 'December 1, 2024',
                                    tags: ['TOEFL: 100+', 'GRE/GMAT Required']
                                }
                            ],
                            'New Haven': [
                                {
                                    name: 'Yale University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=YU',
                                    rank: 9,
                                    programs: 'Law, Medicine, Arts',
                                    tuition: '$55,000 - $60,000 per year',
                                    deadline: 'December 1, 2024',
                                    tags: ['TOEFL: 100+', 'GRE/GMAT Required']
                                }
                            ]
                        }
                    },
                    {
                        name: 'West Coast',
                        flag: '🇺🇸',
                        cities: ['San Francisco', 'Los Angeles', 'Seattle', 'San Diego'],
                        universities: {
                            'San Francisco': [
                                {
                                    name: 'Stanford University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=SU',
                                    rank: 3,
                                    programs: 'Computer Science, Business, Engineering',
                                    tuition: '$52,000 - $56,000 per year',
                                    deadline: 'December 1, 2024',
                                    tags: ['TOEFL: 100+', 'GRE/GMAT Required']
                                },
                                {
                                    name: 'University of California, Berkeley',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UCB',
                                    rank: 13,
                                    programs: 'Engineering, Business, Sciences',
                                    tuition: '$44,000 - $48,000 per year',
                                    deadline: 'December 1, 2024',
                                    tags: ['TOEFL: 90+', 'GRE Required']
                                }
                            ],
                            'Los Angeles': [
                                {
                                    name: 'UCLA',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UCLA',
                                    rank: 19,
                                    programs: 'Film, Business, Engineering',
                                    tuition: '$44,000 - $47,000 per year',
                                    deadline: 'December 1, 2024',
                                    tags: ['TOEFL: 90+', 'GRE Required']
                                }
                            ]
                        }
                    }
                ]
            },
            'canada': {
                title: '🇨🇦 Canada',
                description: 'Canada offers high-quality education with more affordable tuition than the US and pathways to immigration after graduation.',
                countries: [
                    {
                        name: 'Ontario',
                        flag: '🇨🇦',
                        cities: ['Toronto', 'Ottawa', 'Waterloo', 'Hamilton', 'Kingston'],
                        universities: {
                            'Toronto': [
                                {
                                    name: 'University of Toronto',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UoT',
                                    rank: 18,
                                    programs: 'Business, Engineering, Medicine',
                                    tuition: 'CAD $45,000 - $60,000 per year',
                                    deadline: 'January 15, 2025',
                                    tags: ['IELTS: 6.5+', 'GMAT/GRE for some programs']
                                }
                            ],
                            'Waterloo': [
                                {
                                    name: 'University of Waterloo',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UW',
                                    rank: 149,
                                    programs: 'Computer Science, Engineering, Mathematics',
                                    tuition: 'CAD $40,000 - $55,000 per year',
                                    deadline: 'February 1, 2025',
                                    tags: ['IELTS: 6.5+', 'Co-op Programs']
                                }
                            ]
                        }
                    },
                    {
                        name: 'British Columbia',
                        flag: '🇨🇦',
                        cities: ['Vancouver', 'Victoria', 'Kelowna'],
                        universities: {
                            'Vancouver': [
                                {
                                    name: 'University of British Columbia',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UBC',
                                    rank: 34,
                                    programs: 'Forestry, Marine Science, Business',
                                    tuition: 'CAD $42,000 - $58,000 per year',
                                    deadline: 'January 15, 2025',
                                    tags: ['IELTS: 6.5+', 'GMAT/GRE for some programs']
                                }
                            ]
                        }
                    },
                    {
                        name: 'Quebec',
                        flag: '🇨🇦',
                        cities: ['Montreal', 'Quebec City', 'Sherbrooke'],
                        universities: {
                            'Montreal': [
                                {
                                    name: 'McGill University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=MU',
                                    rank: 31,
                                    programs: 'Medicine, Law, Engineering',
                                    tuition: 'CAD $45,000 - $58,000 per year',
                                    deadline: 'January 15, 2025',
                                    tags: ['IELTS: 6.5+', 'French helpful']
                                }
                            ]
                        }
                    }
                ]
            },
            'australia': {
                title: '🇦🇺 Australia & New Zealand',
                description: 'Australia and New Zealand offer high-quality education with a focus on research, innovation, and a great quality of life.',
                countries: [
                    {
                        name: '🇦🇺 Australia',
                        flag: '🇦🇺',
                        cities: ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide'],
                        universities: {
                            'Melbourne': [
                                {
                                    name: 'University of Melbourne',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UoM',
                                    rank: 33,
                                    programs: 'Business, Law, Medicine',
                                    tuition: 'AUD $40,000 - $50,000 per year',
                                    deadline: 'October 31, 2024',
                                    tags: ['IELTS: 6.5+', 'PTE: 58+']
                                }
                            ],
                            'Sydney': [
                                {
                                    name: 'University of Sydney',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UoS',
                                    rank: 38,
                                    programs: 'Engineering, Medicine, Arts',
                                    tuition: 'AUD $42,000 - $52,000 per year',
                                    deadline: 'November 15, 2024',
                                    tags: ['IELTS: 7.0+', 'PTE: 65+']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇳🇿 New Zealand',
                        flag: '🇳🇿',
                        cities: ['Auckland', 'Wellington', 'Christchurch', 'Dunedin'],
                        universities: {
                            'Auckland': [
                                {
                                    name: 'University of Auckland',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UoA',
                                    rank: 85,
                                    programs: 'Business, Engineering, Sciences',
                                    tuition: 'NZD $35,000 - $45,000 per year',
                                    deadline: 'December 1, 2024',
                                    tags: ['IELTS: 6.5+', 'PTE: 58+']
                                }
                            ]
                        }
                    }
                ]
            },
            'asia': {
                title: '🇯🇵 Asia',
                description: 'Asia offers diverse educational opportunities with world-class universities, cutting-edge research, and unique cultural experiences.',
                countries: [
                    {
                        name: '🇯🇵 Japan',
                        flag: '🇯🇵',
                        cities: ['Tokyo', 'Kyoto', 'Osaka', 'Nagoya', 'Fukuoka'],
                        universities: {
                            'Tokyo': [
                                {
                                    name: 'University of Tokyo',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UT',
                                    rank: 23,
                                    programs: 'Engineering, Sciences, Economics',
                                    tuition: '¥535,800/year',
                                    deadline: 'December 15, 2024',
                                    tags: ['JLPT: N2', 'English programs available']
                                }
                            ],
                            'Kyoto': [
                                {
                                    name: 'Kyoto University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=KU',
                                    rank: 33,
                                    programs: 'Sciences, Engineering, Humanities',
                                    tuition: '¥535,800/year',
                                    deadline: 'December 15, 2024',
                                    tags: ['JLPT: N2', 'IELTS: 6.0+']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇨🇳 China',
                        flag: '🇨🇳',
                        cities: ['Beijing', 'Shanghai', 'Guangzhou', 'Hangzhou', 'Nanjing'],
                        universities: {
                            'Beijing': [
                                {
                                    name: 'Tsinghua University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=TU',
                                    rank: 14,
                                    programs: 'Engineering, Computer Science, Business',
                                    tuition: '¥30,000 - ¥40,000/year',
                                    deadline: 'March 31, 2025',
                                    tags: ['HSK: 5', 'English programs available']
                                }
                            ],
                            'Shanghai': [
                                {
                                    name: 'Fudan University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=FU',
                                    rank: 31,
                                    programs: 'Medicine, Economics, International Relations',
                                    tuition: '¥25,000 - ¥45,000/year',
                                    deadline: 'April 30, 2025',
                                    tags: ['HSK: 5', 'IELTS: 6.0+']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇸🇬 Singapore',
                        flag: '🇸🇬',
                        cities: ['Singapore'],
                        universities: {
                            'Singapore': [
                                {
                                    name: 'National University of Singapore',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=NUS',
                                    rank: 11,
                                    programs: 'Business, Engineering, Medicine',
                                    tuition: 'SGD $29,850 - $38,450/year',
                                    deadline: 'February 15, 2025',
                                    tags: ['IELTS: 6.5+', 'English-taught programs']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇰🇷 South Korea',
                        flag: '🇰🇷',
                        cities: ['Seoul', 'Busan', 'Daejeon'],
                        universities: {
                            'Seoul': [
                                {
                                    name: 'Seoul National University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=SNU',
                                    rank: 29,
                                    programs: 'Engineering, Business, Social Sciences',
                                    tuition: '₩6,900,000 - ₩9,800,000/year',
                                    deadline: 'February 28, 2025',
                                    tags: ['TOPIK: Level 3', 'IELTS: 6.0+']
                                }
                            ]
                        }
                    }
                ]
            },
            'middleeast': {
                title: '🇦🇪 Middle East',
                description: 'The Middle East offers a blend of traditional values and modern education with growing research capabilities and international programs.',
                countries: [
                    {
                        name: '🇦🇪 UAE',
                        flag: '🇦🇪',
                        cities: ['Dubai', 'Abu Dhabi', 'Sharjah'],
                        universities: {
                            'Dubai': [
                                {
                                    name: 'American University in Dubai',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=AUD',
                                    rank: 601,
                                    programs: 'Business, Engineering, Media',
                                    tuition: 'AED 90,000 - 100,000/year',
                                    deadline: 'August 1, 2025',
                                    tags: ['IELTS: 6.0+', 'American curriculum']
                                }
                            ],
                            'Abu Dhabi': [
                                {
                                    name: 'New York University Abu Dhabi',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=NYUAD',
                                    rank: 'Not ranked separately',
                                    programs: 'Liberal Arts, Engineering, Sciences',
                                    tuition: 'Need-based financial aid',
                                    deadline: 'January 5, 2025',
                                    tags: ['IELTS: 7.0+', 'SAT/ACT Required']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇹🇷 Turkey',
                        flag: '🇹🇷',
                        cities: ['Istanbul', 'Ankara', 'Izmir'],
                        universities: {
                            'Istanbul': [
                                {
                                    name: 'Koç University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=KU',
                                    rank: 511,
                                    programs: 'Business, Engineering, Social Sciences',
                                    tuition: '$18,000 - $25,000/year',
                                    deadline: 'May 31, 2025',
                                    tags: ['IELTS: 6.5+', 'English-taught programs']
                                }
                            ],
                            'Ankara': [
                                {
                                    name: 'Middle East Technical University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=METU',
                                    rank: 551,
                                    programs: 'Engineering, Sciences, Architecture',
                                    tuition: '$8,000 - $12,000/year',
                                    deadline: 'June 30, 2025',
                                    tags: ['IELTS: 6.0+', 'English-taught programs']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇶🇦 Qatar',
                        flag: '🇶🇦',
                        cities: ['Doha'],
                        universities: {
                            'Doha': [
                                {
                                    name: 'Qatar University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=QU',
                                    rank: 208,
                                    programs: 'Engineering, Business, Islamic Studies',
                                    tuition: 'QAR 36,000 - 48,000/year',
                                    deadline: 'March 1, 2025',
                                    tags: ['IELTS: 5.5+', 'Arabic & English programs']
                                }
                            ]
                        }
                    }
                ]
            },
            'africa': {
                title: '🇿🇦 Africa',
                description: 'Africa offers diverse educational experiences with growing research capabilities and unique cultural perspectives.',
                countries: [
                    {
                        name: '🇿🇦 South Africa',
                        flag: '🇿🇦',
                        cities: ['Cape Town', 'Johannesburg', 'Stellenbosch', 'Pretoria'],
                        universities: {
                            'Cape Town': [
                                {
                                    name: 'University of Cape Town',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UCT',
                                    rank: 237,
                                    programs: 'Medicine, Law, Business',
                                    tuition: 'ZAR 60,000 - 120,000/year',
                                    deadline: 'September 30, 2024',
                                    tags: ['IELTS: 6.5+', 'English-taught programs']
                                }
                            ],
                            'Stellenbosch': [
                                {
                                    name: 'Stellenbosch University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=SU',
                                    rank: 456,
                                    programs: 'Agriculture, Engineering, Arts',
                                    tuition: 'ZAR 55,000 - 100,000/year',
                                    deadline: 'September 30, 2024',
                                    tags: ['IELTS: 6.5+', 'English & Afrikaans']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇪🇬 Egypt',
                        flag: '🇪🇬',
                        cities: ['Cairo', 'Alexandria'],
                        universities: {
                            'Cairo': [
                                {
                                    name: 'American University in Cairo',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=AUC',
                                    rank: 445,
                                    programs: 'Business, Engineering, Middle Eastern Studies',
                                    tuition: '$15,000 - $20,000/year',
                                    deadline: 'February 1, 2025',
                                    tags: ['IELTS: 6.5+', 'American curriculum']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇲🇦 Morocco',
                        flag: '🇲🇦',
                        cities: ['Rabat', 'Casablanca', 'Marrakech'],
                        universities: {
                            'Rabat': [
                                {
                                    name: 'Mohammed V University',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=MV',
                                    rank: 801,
                                    programs: 'Law, Humanities, Sciences',
                                    tuition: 'MAD 30,000 - 50,000/year',
                                    deadline: 'July 15, 2025',
                                    tags: ['French: B2', 'Arabic: B2']
                                }
                            ]
                        }
                    }
                ]
            },
            'southamerica': {
                title: '🇧🇷 South America',
                description: 'South America offers vibrant educational experiences with strong programs in social sciences, arts, and natural resources management.',
                countries: [
                    {
                        name: '🇧🇷 Brazil',
                        flag: '🇧🇷',
                        cities: ['São Paulo', 'Rio de Janeiro', 'Brasília', 'Campinas'],
                        universities: {
                            'São Paulo': [
                                {
                                    name: 'University of São Paulo',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=USP',
                                    rank: 115,
                                    programs: 'Medicine, Engineering, Law',
                                    tuition: 'Free (Public)',
                                    deadline: 'October 31, 2024',
                                    tags: ['Portuguese: B2', 'Limited English programs']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇦🇷 Argentina',
                        flag: '🇦🇷',
                        cities: ['Buenos Aires', 'Córdoba', 'Mendoza'],
                        universities: {
                            'Buenos Aires': [
                                {
                                    name: 'University of Buenos Aires',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=UBA',
                                    rank: 74,
                                    programs: 'Medicine, Law, Social Sciences',
                                    tuition: 'Free (Public)',
                                    deadline: 'December 15, 2024',
                                    tags: ['Spanish: B2', 'Limited English programs']
                                }
                            ]
                        }
                    },
                    {
                        name: '🇨🇱 Chile',
                        flag: '🇨🇱',
                        cities: ['Santiago', 'Valparaíso', 'Concepción'],
                        universities: {
                            'Santiago': [
                                {
                                    name: 'Pontifical Catholic University of Chile',
                                    logo: 'https://via.placeholder.com/80/1e88e5/ffffff?text=PUC',
                                    rank: 127,
                                    programs: 'Engineering, Business, Arts',
                                    tuition: '$8,000 - $12,000/year',
                                    deadline: 'November 30, 2024',
                                    tags: ['Spanish: B2', 'Some English programs']
                                }
                            ]
                        }
                    }
                ]
            }
        };

        // Global variables for navigation
        let currentRegion = null;
        let currentCountry = null;
        let currentCity = null;

        // Function to render region content
        function renderRegionContent(regionKey) {
            console.log("Rendering region:", regionKey);
            const region = regionContent[regionKey];
            if (!region) {
                console.error("Region not found:", regionKey);
                return;
            }

            // Reset navigation state
            currentRegion = regionKey;
            currentCountry = null;
            currentCity = null;

            // Show loading spinner
            const loadingSpinner = document.getElementById('region-loading');
            const contentContainer = document.getElementById('region-content');
            const countryTabsContainer = document.getElementById('country-tabs-container');

            loadingSpinner.style.display = 'block';
            contentContainer.style.opacity = '0.3';
            countryTabsContainer.style.display = 'none'; // Hide country tabs initially

            // Update breadcrumb
            updateBreadcrumb([
                { text: 'Regions', active: false },
                { text: region.title.split(' ')[1] || regionKey, active: true }
            ]);

            // Create region intro
            let regionIntroHTML = `
                <div class="region-intro mb-4">
                    <h2 class="mb-3">${region.title}</h2>
                    <p>${region.description}</p>
                </div>
            `;

            // Render country tabs
            renderCountryTabs(region.countries);

            // Add "Why Study Abroad" section
            let whyStudyAbroadHTML = `
                <div class="why-study-abroad mt-5">
                    <h3 class="section-title">Why Study Abroad?</h3>
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="benefit-card">
                                <div class="benefit-icon">
                                    <i class="fas fa-globe"></i>
                                </div>
                                <h4>Global Perspective</h4>
                                <p>Gain a broader worldview and understand different cultures, which is invaluable in today's interconnected world.</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="benefit-card">
                                <div class="benefit-icon">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <h4>Career Opportunities</h4>
                                <p>International experience is highly valued by employers and can open doors to global career opportunities.</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="benefit-card">
                                <div class="benefit-icon">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                <h4>Academic Excellence</h4>
                                <p>Access world-class education and specialized programs that might not be available in your home country.</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="benefit-card">
                                <div class="benefit-icon">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <h4>Language Skills</h4>
                                <p>Immerse yourself in a new language environment, developing fluency that classroom learning can't provide.</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Combine all HTML
            contentContainer.innerHTML = regionIntroHTML + whyStudyAbroadHTML;

            // Hide loading spinner
            loadingSpinner.style.display = 'none';
            contentContainer.style.opacity = '1';

            // Scroll to the top of the content
            document.getElementById('region-content-container').scrollIntoView({ behavior: 'smooth', block: 'start' });

            // Update map if visible
            if (document.getElementById('map-view-container').style.display !== 'none') {
                updateMapView();
            }
        }

        // Function to render country tabs
        function renderCountryTabs(countries) {
            const countryTabsContainer = document.getElementById('country-tabs-container');
            const countryTabs = document.getElementById('country-tabs');

            // Clear existing tabs
            countryTabs.innerHTML = '';

            // Create tabs for each country
            countries.forEach((country, index) => {
                const tabButton = document.createElement('button');
                tabButton.className = 'country-tab';
                tabButton.setAttribute('data-country', country.name);

                if (index === 0) {
                    tabButton.classList.add('active');
                    // Set the first country as current by default
                    currentCountry = country.name;
                    // Render cities for the first country
                    setTimeout(() => {
                        renderCities(country);
                    }, 100);
                }

                // Remove flag emoji from display name if present
                const displayName = country.name.replace(/^🇬🇧|^🇺🇸|^🇨🇦|^🇦🇺|^🇳🇿|^🇯🇵|^🇨🇳|^🇸🇬|^🇰🇷|^🇦🇪|^🇹🇷|^🇶🇦|^🇿🇦|^🇪🇬|^🇲🇦|^🇧🇷|^🇦🇷|^🇨🇱|^🏴󠁧󠁢󠁥󠁮󠁧󠁿|^🏴󠁧󠁢󠁳󠁣󠁴󠁿|^🇩🇪|^🇫🇷|^🇮🇹|^🇳🇱|^🇪🇸|^🇸🇪/g, '').trim();

                tabButton.innerHTML = `
                    <span class="country-flag">${country.flag}</span> ${displayName}
                `;

                tabButton.addEventListener('click', () => {
                    // Remove active class from all tabs
                    document.querySelectorAll('.country-tab').forEach(tab => {
                        tab.classList.remove('active');
                    });

                    // Add active class to clicked tab
                    tabButton.classList.add('active');

                    // Update current country
                    currentCountry = country.name;
                    currentCity = null;

                    // Render cities for the selected country
                    renderCities(country);
                });

                countryTabs.appendChild(tabButton);
            });

            // Show the country tabs container
            countryTabsContainer.style.display = 'block';

            // Initialize country tabs navigation
            initializeCountryTabsNavigation();
        }

        // Function to initialize country tabs navigation
        function initializeCountryTabsNavigation() {
            const countryTabs = document.getElementById('country-tabs');
            const prevBtn = document.querySelector('.country-tabs-nav-btn.prev');
            const nextBtn = document.querySelector('.country-tabs-nav-btn.next');

            prevBtn.addEventListener('click', () => {
                countryTabs.scrollBy({ left: -300, behavior: 'smooth' });
            });

            nextBtn.addEventListener('click', () => {
                countryTabs.scrollBy({ left: 300, behavior: 'smooth' });
            });
        }

        // Function to render cities for a country
        function renderCities(country) {
            // Get region content container
            const regionContent = document.getElementById('region-content');

            // Find or create cities container
            let citiesContainer = document.getElementById('cities-container');
            if (!citiesContainer) {
                citiesContainer = document.createElement('div');
                citiesContainer.id = 'cities-container';
                citiesContainer.className = 'cities-container';

                // Insert after region intro
                const regionIntro = document.querySelector('.region-intro');
                if (regionIntro && regionIntro.nextSibling) {
                    regionContent.insertBefore(citiesContainer, regionIntro.nextSibling);
                } else {
                    regionContent.insertBefore(citiesContainer, document.querySelector('.why-study-abroad'));
                }
            }

            // Clear existing cities
            citiesContainer.innerHTML = '';

            // Get region title
            const region = regionContent[currentRegion];
            const regionTitle = region ? (region.title.split(' ')[1] || currentRegion) : currentRegion;

            // Update breadcrumb
            updateBreadcrumb([
                { text: 'Regions', active: false, onClick: 'region' },
                { text: regionTitle, active: false, onClick: 'region' },
                { text: country.name.replace(/^🇬🇧|^🇺🇸|^🇨🇦|^🇦🇺|^🇳🇿|^🇯🇵|^🇨🇳|^🇸🇬|^🇰🇷|^🇦🇪|^🇹🇷|^🇶🇦|^🇿🇦|^🇪🇬|^🇲🇦|^🇧🇷|^🇦🇷|^🇨🇱|^🏴󠁧󠁢󠁥󠁮󠁧󠁿|^🏴󠁧󠁢󠁳󠁣󠁴󠁿|^🇩🇪|^🇫🇷|^🇮🇹|^🇳🇱|^🇪🇸|^🇸🇪/g, '').trim(), active: true }
            ]);

            // Create city buttons
            country.cities.forEach((city, index) => {
                const cityButton = document.createElement('div');
                cityButton.className = 'city-button';
                cityButton.setAttribute('data-city', city);

                // Count universities in this city
                const uniCount = country.universities[city] ? country.universities[city].length : 0;

                cityButton.innerHTML = `
                    <div class="city-icon">
                        <i class="fas fa-city"></i>
                    </div>
                    <div class="city-info">
                        <div class="city-name">${city}</div>
                        <div class="city-uni-count">${uniCount} ${uniCount === 1 ? 'University' : 'Universities'}</div>
                    </div>
                `;

                cityButton.addEventListener('click', () => {
                    // Remove active class from all city buttons
                    document.querySelectorAll('.city-button').forEach(btn => {
                        btn.classList.remove('active');
                    });

                    // Add active class to clicked button
                    cityButton.classList.add('active');

                    // Update current city
                    currentCity = city;

                    // Render universities for the selected city
                    renderUniversitiesForCity(country, city);
                });

                citiesContainer.appendChild(cityButton);

                // Activate the first city by default
                if (index === 0) {
                    cityButton.classList.add('active');
                    currentCity = city;
                    renderUniversitiesForCity(country, city);
                }
            });
        }

        // Function to render universities for a city
        function renderUniversitiesForCity(country, city) {
            // Get region content container
            const regionContent = document.getElementById('region-content');

            // Find or create universities container
            let universitiesContainer = document.getElementById('universities-container');
            if (!universitiesContainer) {
                universitiesContainer = document.createElement('div');
                universitiesContainer.id = 'universities-container';
                universitiesContainer.className = 'universities-container mt-4';

                // Insert after cities container
                const citiesContainer = document.getElementById('cities-container');
                if (citiesContainer && citiesContainer.nextSibling) {
                    regionContent.insertBefore(universitiesContainer, citiesContainer.nextSibling);
                } else {
                    regionContent.insertBefore(universitiesContainer, document.querySelector('.why-study-abroad'));
                }
            }

            // Clear existing universities
            universitiesContainer.innerHTML = '';

            // Get region title
            const region = regionContent[currentRegion];
            const regionTitle = region ? (region.title.split(' ')[1] || currentRegion) : currentRegion;

            // Update breadcrumb
            updateBreadcrumb([
                { text: 'Regions', active: false, onClick: 'region' },
                { text: regionTitle, active: false, onClick: 'region' },
                { text: country.name.replace(/^🇬🇧|^🇺🇸|^🇨🇦|^🇦🇺|^🇳🇿|^🇯🇵|^🇨🇳|^🇸🇬|^🇰🇷|^🇦🇪|^🇹🇷|^🇶🇦|^🇿🇦|^🇪🇬|^🇲🇦|^🇧🇷|^🇦🇷|^🇨🇱|^🏴󠁧󠁢󠁥󠁮󠁧󠁿|^🏴󠁧󠁢󠁳󠁣󠁴󠁿|^🇩🇪|^🇫🇷|^🇮🇹|^🇳🇱|^🇪🇸|^🇸🇪/g, '').trim(), active: false, onClick: 'country' },
                { text: city, active: true }
            ]);

            // Get universities for this city
            const universities = country.universities[city] || [];

            if (universities.length === 0) {
                universitiesContainer.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No universities found in ${city}.
                    </div>
                `;
                return;
            }

            // Create university cards
            const universitiesHTML = `
                <h3 class="mb-3">Universities in ${city}</h3>
                <div class="universities-grid">
                    ${universities.map(university => {
                        const uniId = `uni-${university.name.toLowerCase().replace(/\s+/g, '-')}`;
                        return `
                            <div class="university-card" data-university-id="${uniId}" data-region="${currentRegion}" data-country="${country.name}" data-city="${city}">
                                <div class="university-actions">
                                    <button class="btn-bookmark" title="Bookmark this university">
                                        <i class="far fa-bookmark"></i>
                                    </button>
                                    <button class="btn-compare-small" title="Add to comparison">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                                <div class="university-rank">
                                    <span class="rank-badge">QS Rank: ${university.rank}</span>
                                </div>
                                <div class="university-logo">
                                    <img src="${university.logo}" alt="${university.name}">
                                </div>
                                <h4 class="university-name">${university.name}</h4>
                                <div class="university-location">
                                    <i class="fas fa-map-marker-alt text-primary"></i>
                                    <span>${city}, ${country.name.replace(/^🇬🇧|^🇺🇸|^🇨🇦|^🇦🇺|^🇳🇿|^🇯🇵|^🇨🇳|^🇸🇬|^🇰🇷|^🇦🇪|^🇹🇷|^🇶🇦|^🇿🇦|^🇪🇬|^🇲🇦|^🇧🇷|^🇦🇷|^🇨🇱|^🏴󠁧󠁢󠁥󠁮󠁧󠁿|^🏴󠁧󠁢󠁳󠁣󠁴󠁿|^🇩🇪|^🇫🇷|^🇮🇹|^🇳🇱|^🇪🇸|^🇸🇪/g, '').trim()}</span>
                                </div>
                                <div class="university-features">
                                    ${university.programs.split(',').slice(0, 3).map(program =>
                                        `<span class="university-feature"><i class="fas fa-graduation-cap"></i>${program.trim()}</span>`
                                    ).join('')}
                                </div>
                                <div class="university-details">
                                    <div class="detail-item">
                                        <i class="fas fa-book-open text-primary"></i>
                                        <span>${university.programs}</span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="fas fa-coins text-primary"></i>
                                        <span>${university.tuition}</span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        <span>Deadline: ${university.deadline}</span>
                                    </div>
                                </div>
                                <div class="university-requirements">
                                    <h5 class="requirements-title">Admission Requirements</h5>
                                    ${university.requirements ? `
                                    <div class="requirements-grid">
                                        ${university.requirements.english ? `
                                        <div class="requirement-item">
                                            <div class="requirement-icon"><i class="fas fa-language"></i></div>
                                            <div class="requirement-content">
                                                <div class="requirement-label">English</div>
                                                <div class="requirement-value">${university.requirements.english}</div>
                                            </div>
                                        </div>
                                        ` : ''}
                                        ${university.requirements.gre ? `
                                        <div class="requirement-item">
                                            <div class="requirement-icon"><i class="fas fa-tasks"></i></div>
                                            <div class="requirement-content">
                                                <div class="requirement-label">GRE/GMAT</div>
                                                <div class="requirement-value">${university.requirements.gre}</div>
                                            </div>
                                        </div>
                                        ` : ''}
                                        ${university.requirements.gpa ? `
                                        <div class="requirement-item">
                                            <div class="requirement-icon"><i class="fas fa-chart-line"></i></div>
                                            <div class="requirement-content">
                                                <div class="requirement-label">GPA</div>
                                                <div class="requirement-value">${university.requirements.gpa}</div>
                                            </div>
                                        </div>
                                        ` : ''}
                                    </div>
                                    ` : ''}
                                </div>
                                <div class="university-tags mb-3">
                                    ${university.tags.map(tag => `<span class="university-tag">${tag}</span>`).join('')}
                                </div>
                                <div class="university-actions-footer">
                                    <button class="btn btn-primary w-100 mb-2 btn-view-details" data-university-id="${uniId}">
                                        <i class="fas fa-info-circle me-2"></i>More Info
                                    </button>
                                    <div class="action-buttons-row">
                                        <button class="btn btn-outline-primary flex-grow-1 btn-compare" data-university-id="${uniId}">
                                            <i class="fas fa-exchange-alt me-2"></i>Compare
                                        </button>
                                        <button class="btn btn-outline-primary flex-grow-1 btn-bookmark" data-university-id="${uniId}">
                                            <i class="far fa-bookmark me-2"></i>Bookmark
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;

            universitiesContainer.innerHTML = universitiesHTML;

            // Initialize university cards
            initializeUniversityCards();

            // Update map if visible
            if (document.getElementById('map-view-container').style.display !== 'none') {
                updateMapView();
            }
        }

        // Function to update breadcrumb
        function updateBreadcrumb(items) {
            const breadcrumb = document.getElementById('navigation-breadcrumb');
            let html = '';

            items.forEach((item, index) => {
                if (index > 0) {
                    html += '<li class="breadcrumb-item separator"><i class="fas fa-chevron-right"></i></li>';
                }

                if (item.active) {
                    html += `<li class="breadcrumb-item active" aria-current="page">${item.text}</li>`;
                } else if (item.onClick) {
                    html += `<li class="breadcrumb-item"><a href="#" data-action="${item.onClick}">${item.text}</a></li>`;
                } else {
                    html += `<li class="breadcrumb-item">${item.text}</li>`;
                }
            });

            breadcrumb.innerHTML = html;

            // Add event listeners to breadcrumb links
            breadcrumb.querySelectorAll('[data-action]').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.getAttribute('data-action');
                    if (action === 'region') {
                        renderRegionContent(currentRegion);
                    } else if (action === 'country') {
                        showCountry(currentCountry);
                    }
                });
            });
        }

        // Function to show a specific country
        function showCountry(countryName) {
            // Find the country in the current region
            const region = regionContent[currentRegion];
            if (!region) return;

            const country = region.countries.find(c => c.name === countryName);
            if (!country) return;

            // Activate the country tab
            const countryTabs = document.querySelectorAll('.country-tab');
            countryTabs.forEach(tab => {
                if (tab.getAttribute('data-country') === countryName) {
                    tab.click();
                }
            });
        }

        // Function to render default content (when no region is selected)
        function renderDefaultContent() {
            // Reset navigation state
            currentRegion = null;
            currentCountry = null;
            currentCity = null;

            // Hide country tabs container
            const countryTabsContainer = document.getElementById('country-tabs-container');
            countryTabsContainer.style.display = 'none';

            // Update breadcrumb
            updateBreadcrumb([
                { text: 'Select a Region', active: true }
            ]);

            // Get region content container
            const contentContainer = document.getElementById('region-content');

            // Create default content
            const defaultContentHTML = `
                <div class="default-content text-center py-5">
                    <div class="default-content-icon mb-4">
                        <i class="fas fa-globe-americas fa-4x text-primary"></i>
                    </div>
                    <h2 class="mb-3">Explore Universities Worldwide</h2>
                    <p class="lead mb-4">Please select a region to begin exploring universities.</p>
                    <div class="region-selection-help">
                        <div class="step">
                            <div class="step-number">1</div>
                            <div class="step-text">Select a region from the tabs above</div>
                        </div>
                        <div class="step">
                            <div class="step-number">2</div>
                            <div class="step-text">Choose a country from the country tabs</div>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-text">Select a city to view universities</div>
                        </div>
                    </div>
                </div>

                <!-- Why Study Abroad Section -->
                <section class="why-study-abroad mt-5" data-aos="fade-up">
                    <div class="section-header">
                        <h2 class="section-title">Why Study Abroad?</h2>
                        <p class="section-subtitle">Studying abroad offers numerous benefits for your academic and personal growth</p>
                    </div>

                    <div class="benefits-grid mt-4">
                        <div class="benefit-card" data-aos="fade-up" data-aos-delay="100">
                            <div class="benefit-icon">
                                <i class="fas fa-globe-americas"></i>
                            </div>
                            <h3 class="benefit-title">Global Perspective</h3>
                            <p class="benefit-text">Gain a broader worldview and cultural understanding through international exposure.</p>
                        </div>

                        <div class="benefit-card" data-aos="fade-up" data-aos-delay="200">
                            <div class="benefit-icon">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <h3 class="benefit-title">Career Opportunities</h3>
                            <p class="benefit-text">Enhance your resume and access international job markets with global experience.</p>
                        </div>

                        <div class="benefit-card" data-aos="fade-up" data-aos-delay="300">
                            <div class="benefit-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <h3 class="benefit-title">Language Skills</h3>
                            <p class="benefit-text">Improve language proficiency through immersion in a native-speaking environment.</p>
                        </div>

                        <div class="benefit-card" data-aos="fade-up" data-aos-delay="400">
                            <div class="benefit-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <h3 class="benefit-title">Quality Education</h3>
                            <p class="benefit-text">Access world-class teaching and research facilities at top global institutions.</p>
                        </div>
                    </div>
                </section>
            `;

            // Set content
            contentContainer.innerHTML = defaultContentHTML;
        }

        // Function to initialize university cards
        function initializeUniversityCards() {
            // Add event listeners to country headers
            document.querySelectorAll('.country-header').forEach(header => {
                header.addEventListener('click', function() {
                    const icon = this.querySelector('.fa-chevron-down');
                    icon.classList.toggle('rotate-icon');
                });
            });

            // Add event listeners to university cards
            document.querySelectorAll('.btn-view-details').forEach(btn => {
                btn.addEventListener('click', function() {
                    const uniId = this.getAttribute('data-university-id');
                    const card = document.querySelector(`.university-card[data-university-id="${uniId}"]`);
                    const region = card.getAttribute('data-region');
                    const country = card.getAttribute('data-country');
                    const city = card.getAttribute('data-city');
                    const name = card.querySelector('.university-name').textContent;

                    // In a real app, this would navigate to a university detail page
                    alert(`View details for ${name} in ${city}, ${country}`);
                });
            });
        }

        // Handle navigation active states
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

            // Set active link based on current page
            const currentPage = window.location.pathname.split('/').pop() || 'index.html';
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if ((currentPage === 'index.html' && (href === '#' || href === 'index.html')) ||
                    (href === currentPage)) {
                    link.classList.add('active');
                }
            });

            // Handle click events
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');
                });
            });

            // Region tabs functionality
            const regionTabs = document.querySelectorAll('.region-tab');
            console.log("Found region tabs:", regionTabs.length);

            regionTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    console.log("Tab clicked:", this.getAttribute('data-region'));

                    // Remove active class from all tabs
                    regionTabs.forEach(t => t.classList.remove('active'));

                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Update content based on selected region
                    const region = this.getAttribute('data-region');
                    console.log("Updating content for region:", region);
                    renderRegionContent(region);

                    // Hide any previously shown university containers
                    const universitiesContainer = document.getElementById('universities-container');
                    if (universitiesContainer) {
                        universitiesContainer.style.display = 'none';
                    }
                });
            });

            // Mobile menu toggle
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            if (navbarToggler) {
                navbarToggler.addEventListener('click', function() {
                    navbarCollapse.classList.toggle('show');
                });
            }

            // Initialize with default content (no region selected)
            console.log("Initializing with default content");
            renderDefaultContent();

            // Global variables for enhanced features
            let bookmarkedUniversities = JSON.parse(localStorage.getItem('bookmarkedUniversities')) || [];
            let comparisonList = [];
            let universityMap = null;

            // Update bookmark count display
            updateBookmarkCount();

            // Initialize toggle buttons
            initializeToggleButtons();

            // Initialize search functionality
            initializeSearch();

            // Initialize map view
            initializeMapView();

            // Initialize bookmark functionality
            initializeBookmarkButtons();

            // Initialize comparison functionality
            initializeComparisonButtons();

            // Initialize filter functionality
            initializeFilters();
        });

        // Function to initialize toggle buttons
        function initializeToggleButtons() {
            // Filter toggle
            document.getElementById('filter-toggle').addEventListener('click', function() {
                const filtersPanel = document.getElementById('advanced-filters');
                filtersPanel.style.display = filtersPanel.style.display === 'none' ? 'block' : 'none';
            });

            // Map view toggle
            document.getElementById('map-view-toggle').addEventListener('click', function() {
                const mapContainer = document.getElementById('map-view-container');
                mapContainer.style.display = mapContainer.style.display === 'none' ? 'block' : 'none';

                if (mapContainer.style.display === 'block' && !universityMap) {
                    // Initialize map if it's not already initialized
                    initializeMapWithUniversities();
                }
            });

            // Comparison toggle
            document.getElementById('comparison-toggle').addEventListener('click', function() {
                const comparisonContainer = document.getElementById('comparison-container');
                comparisonContainer.style.display = comparisonContainer.style.display === 'none' ? 'block' : 'none';

                if (comparisonContainer.style.display === 'block') {
                    updateComparisonTable();
                }
            });

            // Bookmarks toggle
            document.getElementById('bookmarks-toggle').addEventListener('click', function() {
                const bookmarksContainer = document.getElementById('bookmarks-container');
                bookmarksContainer.style.display = bookmarksContainer.style.display === 'none' ? 'block' : 'none';

                if (bookmarksContainer.style.display === 'block') {
                    renderBookmarkedUniversities();
                }
            });
        }

        // Function to initialize search functionality
        function initializeSearch() {
            const searchInput = document.getElementById('university-search');
            const suggestionsContainer = document.getElementById('search-suggestions');

            searchInput.addEventListener('input', function() {
                const query = this.value.toLowerCase().trim();

                if (query.length < 2) {
                    suggestionsContainer.style.display = 'none';
                    return;
                }

                // Get all universities from the current view
                const universities = document.querySelectorAll('.university-card');
                const matches = [];

                universities.forEach(uni => {
                    const name = uni.querySelector('.university-name').textContent.toLowerCase();
                    const programs = uni.querySelector('.detail-item:nth-child(1) span').textContent.toLowerCase();
                    const location = uni.querySelector('.university-location span').textContent.toLowerCase();

                    if (name.includes(query) || programs.includes(query) || location.includes(query)) {
                        matches.push({
                            id: uni.getAttribute('data-university-id'),
                            name: uni.querySelector('.university-name').textContent,
                            type: name.includes(query) ? 'university' : (programs.includes(query) ? 'program' : 'location'),
                            text: name.includes(query) ? name : (programs.includes(query) ? programs : location)
                        });
                    }
                });

                // Display suggestions
                if (matches.length > 0) {
                    let suggestionsHTML = '';
                    matches.slice(0, 5).forEach(match => {
                        const icon = match.type === 'university' ? 'fa-university' :
                                    (match.type === 'program' ? 'fa-book-open' : 'fa-map-marker-alt');

                        suggestionsHTML += `
                            <div class="search-suggestion-item" data-university-id="${match.id}">
                                <span class="suggestion-icon"><i class="fas ${icon}"></i></span>
                                <span class="suggestion-text">${match.name}</span>
                                <span class="suggestion-category">${match.type}</span>
                            </div>
                        `;
                    });

                    suggestionsContainer.innerHTML = suggestionsHTML;
                    suggestionsContainer.style.display = 'block';

                    // Add click event to suggestions
                    document.querySelectorAll('.search-suggestion-item').forEach(item => {
                        item.addEventListener('click', function() {
                            const uniId = this.getAttribute('data-university-id');
                            const uniCard = document.querySelector(`.university-card[data-university-id="${uniId}"]`);

                            if (uniCard) {
                                uniCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                uniCard.classList.add('highlight-card');
                                setTimeout(() => {
                                    uniCard.classList.remove('highlight-card');
                                }, 2000);

                                searchInput.value = '';
                                suggestionsContainer.style.display = 'none';
                            }
                        });
                    });
                } else {
                    suggestionsContainer.style.display = 'none';
                }
            });

            // Hide suggestions when clicking outside
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                    suggestionsContainer.style.display = 'none';
                }
            });
        }

        // Function to initialize map view
        function initializeMapView() {
            // Map will be initialized when the map view is toggled on
        }

        // Function to initialize map with universities
        function initializeMapWithUniversities() {
            const mapContainer = document.getElementById('university-map');

            // Clear placeholder
            mapContainer.innerHTML = '';

            // Initialize map
            universityMap = L.map(mapContainer).setView([51.505, -0.09], 5);

            // Add tile layer
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(universityMap);

            // Add university markers
            addUniversityMarkersToMap();
        }

        // Function to add university markers to the map
        function addUniversityMarkersToMap() {
            // Clear existing markers if any
            if (universityMap._layers) {
                Object.keys(universityMap._layers).forEach(key => {
                    if (universityMap._layers[key]._icon) {
                        universityMap.removeLayer(universityMap._layers[key]);
                    }
                });
            }

            // Get all visible universities
            const universities = document.querySelectorAll('.university-card:not([style*="display: none"])');

            if (universities.length === 0) {
                return;
            }

            // Create bounds for fitting the map
            const bounds = L.latLngBounds();

            // Add markers for each university
            universities.forEach(university => {
                const uniId = university.getAttribute('data-university-id');
                const region = university.getAttribute('data-region');
                const country = university.getAttribute('data-country');
                const city = university.getAttribute('data-city');

                // Find the university data in the regionContent
                const universityData = findUniversityByIdInRegion(uniId, region, country, city);

                if (universityData && universityData.location) {
                    const { lat, lng } = universityData.location;

                    // Create marker
                    const marker = L.marker([lat, lng]).addTo(universityMap);

                    // Create popup content
                    const popupContent = `
                        <div class="map-popup">
                            <img src="${universityData.logo}" alt="${universityData.name}" class="map-popup-logo" style="width:60px;height:60px;object-fit:contain;margin:0 auto 10px;display:block;">
                            <h5 style="font-size:16px;margin-bottom:5px;">${universityData.name}</h5>
                            <p style="font-size:14px;margin-bottom:5px;">QS Rank: ${universityData.rank}</p>
                            <p style="font-size:14px;margin-bottom:10px;">${city}, ${country.replace(/^[^A-Za-z]+/, '')}</p>
                            <button class="btn btn-sm btn-primary map-view-details" data-university-id="${uniId}" style="width:100%;">
                                View Details
                            </button>
                        </div>
                    `;

                    // Bind popup to marker
                    marker.bindPopup(popupContent);

                    // Extend bounds to include this marker
                    bounds.extend([lat, lng]);
                }
            });

            // Fit map to bounds if we have markers
            if (bounds.isValid()) {
                universityMap.fitBounds(bounds, { padding: [50, 50] });
            }
        }

        // Helper function to find university data in regionContent
        function findUniversityByIdInRegion(uniId, region, country, city) {
            // Extract university name from uniId
            const uniName = uniId.replace('uni-', '').replace(/-/g, ' ');

            // Find the university in the data
            const regionData = regionContent[region];
            if (!regionData) return null;

            for (const countryData of regionData.countries) {
                if (countryData.name === country) {
                    const universities = countryData.universities[city];
                    if (universities) {
                        for (const university of universities) {
                            if (university.name.toLowerCase() === uniName ||
                                uniId === `uni-${university.name.toLowerCase().replace(/\s+/g, '-')}`) {
                                return university;
                            }
                        }
                    }
                }
            }

            return null;
        }

        // Function to update map view when content changes
        function updateMapView() {
            if (universityMap) {
                // Add markers for visible universities
                addUniversityMarkersToMap();
            }
        }

        // Function to highlight a university card
        function highlightUniversityCard(uniId) {
            // Remove highlight from all cards
            document.querySelectorAll('.university-card').forEach(card => {
                card.classList.remove('highlight-card');
            });

            // Add highlight to the selected card
            const card = document.querySelector(`.university-card[data-university-id="${uniId}"]`);
            if (card) {
                card.classList.add('highlight-card');

                // Remove highlight after 2 seconds
                setTimeout(() => {
                    card.classList.remove('highlight-card');
                }, 2000);
            }
        }

        // Function to initialize bookmark buttons
        function initializeBookmarkButtons() {
            // Add event delegation for bookmark buttons
            document.addEventListener('click', function(e) {
                if (e.target.closest('.btn-bookmark')) {
                    const bookmarkBtn = e.target.closest('.btn-bookmark');
                    const universityCard = bookmarkBtn.closest('.university-card');
                    const universityId = universityCard.getAttribute('data-university-id');
                    const universityName = universityCard.querySelector('.university-name').textContent;

                    toggleBookmark(universityId, universityName, universityCard);
                }
            });

            // Update bookmark button states
            updateBookmarkButtonStates();
        }

        // Function to toggle bookmark
        function toggleBookmark(universityId, universityName, universityCard) {
            const index = bookmarkedUniversities.findIndex(uni => uni.id === universityId);

            if (index === -1) {
                // Add to bookmarks
                const universityData = {
                    id: universityId,
                    name: universityName,
                    html: universityCard.outerHTML
                };

                bookmarkedUniversities.push(universityData);
                universityCard.querySelector('.btn-bookmark i').classList.remove('far');
                universityCard.querySelector('.btn-bookmark i').classList.add('fas');
                universityCard.querySelector('.btn-bookmark').classList.add('active');

                // Show success message
                showToast(`${universityName} added to bookmarks`, 'success');
            } else {
                // Remove from bookmarks
                bookmarkedUniversities.splice(index, 1);
                universityCard.querySelector('.btn-bookmark i').classList.remove('fas');
                universityCard.querySelector('.btn-bookmark i').classList.add('far');
                universityCard.querySelector('.btn-bookmark').classList.remove('active');

                // Show info message
                showToast(`${universityName} removed from bookmarks`, 'info');
            }

            // Save to localStorage
            localStorage.setItem('bookmarkedUniversities', JSON.stringify(bookmarkedUniversities));

            // Update bookmark count
            updateBookmarkCount();

            // Update bookmarked universities display if visible
            if (document.getElementById('bookmarks-container').style.display === 'block') {
                renderBookmarkedUniversities();
            }
        }

        // Function to show toast notification
        function showToast(message, type = 'info') {
            // Create toast container if it doesn't exist
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }

            // Create toast element
            const toastId = 'toast-' + Date.now();
            const toastHTML = `
                <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header ${type === 'success' ? 'bg-success text-white' : 'bg-info text-white'}">
                        <strong class="me-auto">${type === 'success' ? 'Success' : 'Information'}</strong>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;

            // Add toast to container
            toastContainer.insertAdjacentHTML('beforeend', toastHTML);

            // Initialize and show toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
            toast.show();

            // Remove toast after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }

        // Function to update bookmark button states
        function updateBookmarkButtonStates() {
            document.querySelectorAll('.university-card').forEach(card => {
                const universityId = card.getAttribute('data-university-id');
                const isBookmarked = bookmarkedUniversities.some(uni => uni.id === universityId);

                if (isBookmarked) {
                    card.querySelector('.btn-bookmark i').classList.remove('far');
                    card.querySelector('.btn-bookmark i').classList.add('fas');
                    card.querySelector('.btn-bookmark').classList.add('active');
                }
            });
        }

        // Function to update bookmark count
        function updateBookmarkCount() {
            const count = bookmarkedUniversities.length;
            document.querySelector('.bookmark-count').textContent = count;
        }

        // Function to render bookmarked universities
        function renderBookmarkedUniversities() {
            const container = document.getElementById('bookmarked-universities');

            if (bookmarkedUniversities.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-bookmark fa-3x text-primary mb-3"></i>
                        <h5>No Bookmarks Yet</h5>
                        <p>Click the bookmark icon on university cards to save them here</p>
                    </div>
                `;
                return;
            }

            let html = '';
            bookmarkedUniversities.forEach(uni => {
                html += uni.html;
            });

            container.innerHTML = html;

            // Reinitialize bookmark buttons in the bookmarked universities
            updateBookmarkButtonStates();
        }

        // Function to initialize comparison buttons
        function initializeComparisonButtons() {
            // Add event delegation for comparison buttons
            document.addEventListener('click', function(e) {
                if (e.target.closest('.btn-compare')) {
                    const compareBtn = e.target.closest('.btn-compare');
                    const universityCard = compareBtn.closest('.university-card');
                    const universityId = universityCard.getAttribute('data-university-id');

                    toggleComparison(universityId, universityCard);
                }
            });
        }

        // Function to toggle comparison
        function toggleComparison(universityId, universityCard) {
            const index = comparisonList.findIndex(uni => uni.id === universityId);

            if (index === -1) {
                // Add to comparison
                if (comparisonList.length >= 3) {
                    showToast('You can compare up to 3 universities at a time. Please remove one before adding another.', 'warning');
                    return;
                }

                // Get requirements if available
                let requirements = {};
                const requirementsSection = universityCard.querySelector('.university-requirements');
                if (requirementsSection) {
                    const englishReq = requirementsSection.querySelector('.requirement-item:nth-child(1) .requirement-value')?.textContent || 'Not specified';
                    const greReq = requirementsSection.querySelector('.requirement-item:nth-child(2) .requirement-value')?.textContent || 'Not specified';
                    const gpaReq = requirementsSection.querySelector('.requirement-item:nth-child(3) .requirement-value')?.textContent || 'Not specified';

                    requirements = {
                        english: englishReq,
                        gre: greReq,
                        gpa: gpaReq
                    };
                }

                const universityData = {
                    id: universityId,
                    name: universityCard.querySelector('.university-name').textContent,
                    logo: universityCard.querySelector('.university-logo img').src,
                    rank: universityCard.querySelector('.rank-badge').textContent.replace('QS Rank: ', ''),
                    programs: universityCard.querySelector('.detail-item:nth-child(1) span').textContent,
                    tuition: universityCard.querySelector('.detail-item:nth-child(2) span').textContent,
                    deadline: universityCard.querySelector('.detail-item:nth-child(3) span').textContent,
                    location: universityCard.querySelector('.university-location span').textContent,
                    tags: Array.from(universityCard.querySelectorAll('.university-tag')).map(tag => tag.textContent),
                    requirements: requirements
                };

                comparisonList.push(universityData);
                universityCard.querySelector('.btn-compare').textContent = 'Remove from Compare';
                universityCard.querySelector('.btn-compare').classList.add('active');
            } else {
                // Remove from comparison
                comparisonList.splice(index, 1);
                universityCard.querySelector('.btn-compare').innerHTML = '<i class="fas fa-exchange-alt"></i> Add to Compare';
                universityCard.querySelector('.btn-compare').classList.remove('active');
            }

            // Update comparison table if visible
            if (document.getElementById('comparison-container').style.display === 'block') {
                updateComparisonTable();
            }
        }

        // Function to update comparison table
        function updateComparisonTable() {
            const container = document.getElementById('comparison-table');

            if (comparisonList.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-exchange-alt fa-3x text-primary mb-3"></i>
                        <h5>No Universities Selected</h5>
                        <p>Click the "Add to Compare" button on university cards to add them here</p>
                    </div>
                `;
                return;
            }

            let html = '<table class="table">';

            // Headers
            html += '<tr><th></th>';
            comparisonList.forEach(uni => {
                html += `
                    <th class="comparison-university-header">
                        <img src="${uni.logo}" alt="${uni.name}" class="comparison-university-logo">
                        <h5>${uni.name}</h5>
                        <button class="comparison-remove-btn" data-university-id="${uni.id}">
                            <i class="fas fa-times"></i>
                        </button>
                    </th>
                `;
            });
            html += '</tr>';

            // Rows
            const rows = [
                { label: 'Location', property: 'location' },
                { label: 'QS Ranking', property: 'rank' },
                { label: 'Programs', property: 'programs' },
                { label: 'Tuition', property: 'tuition' },
                { label: 'Application Deadline', property: 'deadline' },
                { label: 'English Requirements', property: 'requirements.english', fallback: 'Not specified' },
                { label: 'GRE/GMAT', property: 'requirements.gre', fallback: 'Not specified' },
                { label: 'GPA', property: 'requirements.gpa', fallback: 'Not specified' },
                { label: 'Tags', property: 'tags' }
            ];

            rows.forEach(row => {
                html += `<tr><th>${row.label}</th>`;

                comparisonList.forEach(uni => {
                    let value;

                    // Handle nested properties like 'requirements.english'
                    if (row.property.includes('.')) {
                        const [obj, prop] = row.property.split('.');
                        value = uni[obj] && uni[obj][prop] ? uni[obj][prop] : row.fallback || 'Not specified';
                    } else if (row.property === 'tags') {
                        value = uni.tags.join(', ');
                    } else {
                        value = uni[row.property];
                    }

                    html += `<td>${value}</td>`;
                });

                html += '</tr>';
            });

            html += '</table>';
            container.innerHTML = html;

            // Add event listeners to remove buttons
            document.querySelectorAll('.comparison-remove-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const universityId = this.getAttribute('data-university-id');
                    const universityCard = document.querySelector(`.university-card[data-university-id="${universityId}"]`);

                    if (universityCard) {
                        toggleComparison(universityId, universityCard);
                    } else {
                        // If card is not in the current view, just remove from comparison
                        const index = comparisonList.findIndex(uni => uni.id === universityId);
                        if (index !== -1) {
                            comparisonList.splice(index, 1);
                            updateComparisonTable();
                        }
                    }
                });
            });
        }

        // Function to initialize filters
        function initializeFilters() {
            document.getElementById('apply-filters').addEventListener('click', function() {
                applyFilters();
            });

            document.getElementById('reset-filters').addEventListener('click', function() {
                resetFilters();
            });
        }

        // Function to apply filters
        function applyFilters() {
            const fieldFilter = document.getElementById('field-filter').value;
            const tuitionFilter = document.getElementById('tuition-filter').value;
            const languageFilter = document.getElementById('language-filter').value;
            const scholarshipFilter = document.getElementById('scholarship-filter').value;
            const rankFilter = document.getElementById('rank-filter').value;
            const gpaFilter = document.getElementById('gpa-filter').value;

            // Get all university cards
            const cards = document.querySelectorAll('.university-card');

            // Show loading indicator
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'text-center my-4';
            loadingIndicator.id = 'filter-loading';
            loadingIndicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">Applying filters...</p>';

            const universitiesContainer = document.querySelector('.universities-grid');
            if (universitiesContainer) {
                universitiesContainer.appendChild(loadingIndicator);
            }

            // Apply filters after a short delay to allow UI to update
            setTimeout(() => {
                // Apply filters to cards
                applyFiltersToCards(cards);

                // Remove loading indicator
                const loadingElement = document.getElementById('filter-loading');
                if (loadingElement && loadingElement.parentNode) {
                    loadingElement.parentNode.removeChild(loadingElement);
                }

                // Show toast with filter results
                const visibleCount = document.querySelectorAll('.university-card:not([style*="display: none"])').length;
                showToast(`Found ${visibleCount} universities matching your filters`, 'info');

                // Update map if visible
                if (document.getElementById('map-view-container').style.display === 'block') {
                    updateMapView();
                }
            }, 100);
        }

        // Function to apply filters to cards
        function applyFiltersToCards(cards) {
            const fieldFilter = document.getElementById('field-filter').value;
            const tuitionFilter = document.getElementById('tuition-filter').value;
            const languageFilter = document.getElementById('language-filter').value;
            const scholarshipFilter = document.getElementById('scholarship-filter').value;
            const rankFilter = document.getElementById('rank-filter').value;
            const gpaFilter = document.getElementById('gpa-filter').value;

            cards.forEach(card => {
                let visible = true;

                // Field filter
                if (fieldFilter && visible) {
                    const programs = card.querySelector('.detail-item:nth-child(1) span').textContent.toLowerCase();
                    visible = programs.includes(fieldFilter.toLowerCase());
                }

                // Tuition filter
                if (tuitionFilter && visible) {
                    const tuition = card.querySelector('.detail-item:nth-child(2) span').textContent.toLowerCase();

                    switch (tuitionFilter) {
                        case 'low':
                            visible = tuition.includes('free') ||
                                    (tuition.includes('£') && parseInt(tuition.replace(/[^0-9]/g, '')) < 15000) ||
                                    (tuition.includes('$') && parseInt(tuition.replace(/[^0-9]/g, '')) < 15000);
                            break;
                        case 'medium':
                            visible = (tuition.includes('£') &&
                                    parseInt(tuition.replace(/[^0-9]/g, '')) >= 15000 &&
                                    parseInt(tuition.replace(/[^0-9]/g, '')) < 30000) ||
                                    (tuition.includes('$') &&
                                    parseInt(tuition.replace(/[^0-9]/g, '')) >= 15000 &&
                                    parseInt(tuition.replace(/[^0-9]/g, '')) < 30000);
                            break;
                        case 'high':
                            visible = (tuition.includes('£') &&
                                    parseInt(tuition.replace(/[^0-9]/g, '')) >= 30000 &&
                                    parseInt(tuition.replace(/[^0-9]/g, '')) < 50000) ||
                                    (tuition.includes('$') &&
                                    parseInt(tuition.replace(/[^0-9]/g, '')) >= 30000 &&
                                    parseInt(tuition.replace(/[^0-9]/g, '')) < 50000);
                            break;
                        case 'very-high':
                            visible = (tuition.includes('£') &&
                                    parseInt(tuition.replace(/[^0-9]/g, '')) >= 50000) ||
                                    (tuition.includes('$') &&
                                    parseInt(tuition.replace(/[^0-9]/g, '')) >= 50000);
                            break;
                    }
                }

                // Language filter
                if (languageFilter && visible) {
                    const tags = Array.from(card.querySelectorAll('.university-tag')).map(tag => tag.textContent.toLowerCase());
                    const requirementsSection = card.querySelector('.university-requirements');
                    let englishReq = '';

                    if (requirementsSection) {
                        englishReq = requirementsSection.querySelector('.requirement-item:nth-child(1) .requirement-value')?.textContent.toLowerCase() || '';
                    }

                    visible = tags.some(tag => tag.includes(languageFilter.toLowerCase())) ||
                             englishReq.includes(languageFilter.toLowerCase());
                }

                // Scholarship filter
                if (scholarshipFilter && visible) {
                    const tags = Array.from(card.querySelectorAll('.university-tag')).map(tag => tag.textContent.toLowerCase());

                    switch (scholarshipFilter) {
                        case 'available':
                            visible = tags.some(tag => tag.includes('scholarship'));
                            break;
                        case 'full':
                            visible = tags.some(tag => tag.includes('full') && tag.includes('scholarship'));
                            break;
                        case 'partial':
                            visible = tags.some(tag => tag.includes('partial') && tag.includes('scholarship'));
                            break;
                        case 'merit':
                            visible = tags.some(tag => tag.includes('merit') && tag.includes('scholarship'));
                            break;
                        case 'need':
                            visible = tags.some(tag => tag.includes('need') && tag.includes('scholarship'));
                            break;
                    }
                }

                // Rank filter
                if (rankFilter && visible) {
                    const rankText = card.querySelector('.rank-badge').textContent;
                    const rank = parseInt(rankText.replace(/[^0-9]/g, ''));

                    if (rankFilter === 'top50' && rank > 50) {
                        visible = false;
                    } else if (rankFilter === 'top100' && rank > 100) {
                        visible = false;
                    } else if (rankFilter === 'top200' && rank > 200) {
                        visible = false;
                    } else if (rankFilter === 'top500' && rank > 500) {
                        visible = false;
                    }
                }

                // GPA filter
                if (gpaFilter && visible) {
                    const requirementsSection = card.querySelector('.university-requirements');
                    if (requirementsSection) {
                        const gpaReq = requirementsSection.querySelector('.requirement-item:nth-child(3) .requirement-value')?.textContent.toLowerCase() || '';

                        if (gpaFilter === 'high' && !gpaReq.includes('3.5')) {
                            visible = false;
                        } else if (gpaFilter === 'medium' && (!gpaReq.includes('3.0') || gpaReq.includes('3.5'))) {
                            visible = false;
                        } else if (gpaFilter === 'low' && (gpaReq.includes('3.0') || gpaReq === 'not specified')) {
                            visible = false;
                        }
                    }
                }

                // Show/hide card
                if (visible) {
                    card.style.display = '';

                    // Also show parent containers (city and country)
                    const citySection = card.closest('.city-section');
                    if (citySection) {
                        citySection.style.display = '';

                        // Show country section
                        const countrySection = citySection.closest('.country-section');
                        if (countrySection) {
                            countrySection.style.display = '';

                            // Show country content
                            const countryContent = countrySection.querySelector('[id$="-content"]');
                            if (countryContent) {
                                countryContent.classList.add('show');
                            }
                        }
                    }
                } else {
                    card.style.display = 'none';
                }
            });

            // Hide empty city sections
            document.querySelectorAll('.city-section').forEach(citySection => {
                const visibleCards = citySection.querySelectorAll('.university-card[style=""]');
                if (visibleCards.length === 0) {
                    citySection.style.display = 'none';
                }
            });

            // Hide empty country sections
            document.querySelectorAll('.country-section').forEach(countrySection => {
                const visibleCities = countrySection.querySelectorAll('.city-section[style=""]');
                if (visibleCities.length === 0) {
                    countrySection.style.display = 'none';
                }
            });

            // Hide filters panel
            document.getElementById('advanced-filters').style.display = 'none';
        }

        // Function to reset filters
        function resetFilters() {
            // Reset filter values
            document.getElementById('field-filter').value = '';
            document.getElementById('tuition-filter').value = '';
            document.getElementById('language-filter').value = '';
            document.getElementById('scholarship-filter').value = '';
            document.getElementById('rank-filter').value = '';
            document.getElementById('gpa-filter').value = '';

            // Show all cards
            document.querySelectorAll('.university-card').forEach(card => {
                card.style.display = '';
            });

            // Show all city sections
            document.querySelectorAll('.city-section').forEach(citySection => {
                citySection.style.display = '';
            });

            // Show all country sections
            document.querySelectorAll('.country-section').forEach(countrySection => {
                countrySection.style.display = '';
            });

            // Hide filters panel
            document.getElementById('advanced-filters').style.display = 'none';
        }

    </script>
</body>
</html>
