<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Go Abroad - Study Abroad Platform</title>
    <style>
        :root {
            --primary-color: #1e88e5;
            --primary-dark: #1565c0;
            --secondary-color: #ff9800;
            --white: #ffffff;
            --light-gray: #f5f7fa;
            --text-dark: #333333;
            --text-light: #666666;
            --border-radius: 8px;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f2f5;
            color: var(--text-dark);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .app {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background-color: var(--primary-color);
            color: var(--white);
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: var(--primary-color);
        }

        .sidebar-title {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .sidebar-nav {
            flex: 1;
            padding: 20px 0;
            overflow-y: auto;
        }

        .sidebar-nav-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-nav-item:hover, .sidebar-nav-item.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--white);
        }

        .sidebar-icon {
            font-size: 1.2rem;
            min-width: 20px;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
        }

        .content-container {
            background-color: var(--white);
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow);
        }

        .page-title {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--primary-dark);
        }

        .page-description {
            color: var(--text-light);
            margin-bottom: 30px;
        }

        .region-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 30px;
        }

        .region-tab {
            background-color: var(--light-gray);
            color: var(--text-dark);
            border: none;
            padding: 10px 20px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .region-tab:hover, .region-tab.active {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .country-section {
            margin-bottom: 30px;
        }

        .country-header {
            background-color: var(--primary-color);
            color: var(--white);
            padding: 15px 20px;
            border-radius: var(--border-radius);
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .universities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .university-card {
            background-color: var(--light-gray);
            padding: 20px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: transform 0.3s ease;
        }

        .university-card:hover {
            transform: translateY(-5px);
        }

        .university-logo {
            text-align: center;
            margin-bottom: 15px;
        }

        .university-logo img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            background-color: var(--primary-color);
            color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .university-name {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: var(--primary-dark);
            text-align: center;
        }

        .university-details {
            margin-bottom: 15px;
        }

        .university-details p {
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .university-details .icon {
            color: var(--primary-color);
        }

        .university-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 15px;
        }

        .university-tag {
            background-color: var(--primary-color);
            color: var(--white);
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .view-details-btn {
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            padding: 8px 15px;
            border-radius: var(--border-radius);
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s ease;
        }

        .view-details-btn:hover {
            background-color: var(--primary-dark);
        }

        .region-content {
            display: none;
        }

        .region-content.active {
            display: block;
        }

        .search-container {
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            max-width: 500px;
            padding: 10px 15px;
            border: 1px solid var(--medium-gray);
            border-radius: 20px;
            font-size: 1rem;
        }

        .countries-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .country-card {
            background-color: var(--white);
            padding: 15px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
        }

        .country-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
            background-color: var(--primary-color);
            color: var(--white);
        }

        .country-card h3 {
            font-size: 1.1rem;
            margin: 0;
        }

        .back-button {
            margin-bottom: 20px;
        }

        .back-button button {
            background-color: var(--light-gray);
            border: none;
            padding: 8px 15px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: background-color 0.3s ease;
            font-weight: 500;
        }

        .back-button button:hover {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .city-header {
            background-color: var(--light-gray);
            padding: 10px 15px;
            border-radius: var(--border-radius);
            margin-bottom: 15px;
        }

        .city-header h3 {
            margin: 0;
            color: var(--primary-dark);
        }

        .no-data-message {
            background-color: var(--light-gray);
            padding: 30px;
            border-radius: var(--border-radius);
            text-align: center;
        }

        .no-data-message h3 {
            margin-bottom: 15px;
            color: var(--primary-dark);
        }

        .contact-advisor-btn {
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            padding: 10px 20px;
            border-radius: var(--border-radius);
            margin-top: 15px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .contact-advisor-btn:hover {
            background-color: var(--primary-dark);
        }

        .why-study-abroad {
            background-color: var(--white);
            padding: 30px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-top: 40px;
        }

        .why-study-abroad h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: var(--primary-dark);
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .benefit-card {
            background-color: var(--light-gray);
            padding: 20px;
            border-radius: var(--border-radius);
            text-align: center;
        }

        .benefit-card .icon {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .benefit-card h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: var(--primary-dark);
        }

        footer {
            background-color: var(--white);
            padding: 20px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-top: 40px;
            text-align: center;
            color: var(--text-light);
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
            }

            .sidebar-title, .sidebar-text {
                display: none;
            }

            .main-content {
                margin-left: 70px;
            }

            .universities-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="app">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">GA</div>
                <h1 class="sidebar-title">Go Abroad</h1>
            </div>

            <div class="sidebar-nav">
                <div class="sidebar-nav-item">
                    <span class="sidebar-icon">🏠</span>
                    <span class="sidebar-text">Home</span>
                </div>

                <div class="sidebar-nav-item active">
                    <span class="sidebar-icon">🔍</span>
                    <span class="sidebar-text">Search Universities</span>
                </div>

                <div class="sidebar-nav-item">
                    <span class="sidebar-icon">🎓</span>
                    <span class="sidebar-text">Scholarships</span>
                </div>

                <div class="sidebar-nav-item">
                    <span class="sidebar-icon">📝</span>
                    <span class="sidebar-text">Applications</span>
                </div>

                <div class="sidebar-nav-item">
                    <span class="sidebar-icon">📚</span>
                    <span class="sidebar-text">Admission Guide</span>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="content-container">
                <h1 class="page-title">Search Universities</h1>
                <p class="page-description">Explore universities by region and find your perfect study destination.</p>

                <div class="region-tabs">
                    <button class="region-tab active" data-region="uk">🇬🇧 UK</button>
                    <button class="region-tab" data-region="europe">🇪🇺 Europe</button>
                    <button class="region-tab" data-region="usa">🇺🇸 USA</button>
                    <button class="region-tab" data-region="canada">🇨🇦 Canada</button>
                    <button class="region-tab" data-region="australia-nz">🇦🇺 Australia & NZ</button>
                </div>

                <!-- UK Region Content -->
                <div id="uk-region" class="region-content active">
                    <div class="country-section">
                        <div class="country-header">
                            <h3>🏴󠁧󠁢󠁥󠁮󠁧󠁿 England</h3>
                        </div>

                        <div class="universities-grid">
                            <div class="university-card">
                                <div class="university-logo">
                                    <img src="https://via.placeholder.com/80/1e88e5/ffffff?text=UCL" alt="UCL">
                                </div>
                                <h4 class="university-name">University College London</h4>
                                <div class="university-details">
                                    <p><span class="icon">🏆</span> QS Rank: 8</p>
                                    <p><span class="icon">📚</span> Programs: Computer Science, Medicine, Law</p>
                                    <p><span class="icon">💰</span> Tuition: £23,000 - £35,000</p>
                                    <p><span class="icon">📅</span> Deadline: January 15, 2025</p>
                                </div>
                                <div class="university-tags">
                                    <span class="university-tag">IELTS: 6.5+</span>
                                    <span class="university-tag">Scholarships</span>
                                </div>
                                <button class="view-details-btn">View Details</button>
                            </div>

                            <div class="university-card">
                                <div class="university-logo">
                                    <img src="https://via.placeholder.com/80/1e88e5/ffffff?text=ICL" alt="Imperial">
                                </div>
                                <h4 class="university-name">Imperial College London</h4>
                                <div class="university-details">
                                    <p><span class="icon">🏆</span> QS Rank: 7</p>
                                    <p><span class="icon">📚</span> Programs: Engineering, Medicine, Science</p>
                                    <p><span class="icon">💰</span> Tuition: £25,000 - £38,000</p>
                                    <p><span class="icon">📅</span> Deadline: February 28, 2025</p>
                                </div>
                                <div class="university-tags">
                                    <span class="university-tag">IELTS: 7.0+</span>
                                    <span class="university-tag">GRE Required</span>
                                </div>
                                <button class="view-details-btn">View Details</button>
                            </div>
                        </div>
                    </div>

                    <div class="country-section">
                        <div class="country-header">
                            <h3>🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scotland</h3>
                        </div>

                        <div class="universities-grid">
                            <div class="university-card">
                                <div class="university-logo">
                                    <img src="https://via.placeholder.com/80/1e88e5/ffffff?text=UoE" alt="Edinburgh">
                                </div>
                                <h4 class="university-name">University of Edinburgh</h4>
                                <div class="university-details">
                                    <p><span class="icon">🏆</span> QS Rank: 15</p>
                                    <p><span class="icon">📚</span> Programs: Computer Science, Medicine, Arts</p>
                                    <p><span class="icon">💰</span> Tuition: £26,000 - £37,000</p>
                                    <p><span class="icon">📅</span> Deadline: May 30, 2024</p>
                                </div>
                                <div class="university-tags">
                                    <span class="university-tag">IELTS: 6.5+</span>
                                    <span class="university-tag">PTE: 62+</span>
                                </div>
                                <button class="view-details-btn">View Details</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Europe Region Content -->
                <div id="europe-region" class="region-content">
                    <h2>🇪🇺 Europe</h2>
                    <p>Europe offers diverse educational opportunities with many programs taught in English and affordable or free tuition in several countries.</p>

                    <div class="search-container">
                        <input type="text" id="country-search" placeholder="Search for a country..." class="search-input">
                    </div>

                    <div class="countries-grid">
                        <div class="country-card" data-country="albania">
                            <h3>🇦🇱 Albania</h3>
                        </div>
                        <div class="country-card" data-country="austria">
                            <h3>🇦🇹 Austria</h3>
                        </div>
                        <div class="country-card" data-country="belgium">
                            <h3>🇧🇪 Belgium</h3>
                        </div>
                        <div class="country-card" data-country="croatia">
                            <h3>🇭🇷 Croatia</h3>
                        </div>
                        <div class="country-card" data-country="cyprus">
                            <h3>🇨🇾 Cyprus</h3>
                        </div>
                        <div class="country-card" data-country="czech-republic">
                            <h3>🇨🇿 Czech Republic</h3>
                        </div>
                        <div class="country-card" data-country="denmark">
                            <h3>🇩🇰 Denmark</h3>
                        </div>
                        <div class="country-card" data-country="estonia">
                            <h3>🇪🇪 Estonia</h3>
                        </div>
                        <div class="country-card" data-country="finland">
                            <h3>🇫🇮 Finland</h3>
                        </div>
                        <div class="country-card" data-country="france">
                            <h3>🇫🇷 France</h3>
                        </div>
                        <div class="country-card" data-country="georgia">
                            <h3>🇬🇪 Georgia</h3>
                        </div>
                        <div class="country-card" data-country="germany">
                            <h3>🇩🇪 Germany</h3>
                        </div>
                        <div class="country-card" data-country="greece">
                            <h3>🇬🇷 Greece</h3>
                        </div>
                        <div class="country-card" data-country="hungary">
                            <h3>🇭🇺 Hungary</h3>
                        </div>
                        <div class="country-card" data-country="iceland">
                            <h3>🇮🇸 Iceland</h3>
                        </div>
                        <div class="country-card" data-country="ireland">
                            <h3>🇮🇪 Ireland</h3>
                        </div>
                        <div class="country-card" data-country="italy">
                            <h3>🇮🇹 Italy</h3>
                        </div>
                        <div class="country-card" data-country="latvia">
                            <h3>🇱🇻 Latvia</h3>
                        </div>
                        <div class="country-card" data-country="lithuania">
                            <h3>🇱🇹 Lithuania</h3>
                        </div>
                        <div class="country-card" data-country="luxembourg">
                            <h3>🇱🇺 Luxembourg</h3>
                        </div>
                        <div class="country-card" data-country="malta">
                            <h3>🇲🇹 Malta</h3>
                        </div>
                        <div class="country-card" data-country="moldova">
                            <h3>🇲🇩 Moldova</h3>
                        </div>
                        <div class="country-card" data-country="montenegro">
                            <h3>🇲🇪 Montenegro</h3>
                        </div>
                        <div class="country-card" data-country="netherlands">
                            <h3>🇳🇱 Netherlands</h3>
                        </div>
                        <div class="country-card" data-country="north-macedonia">
                            <h3>🇲🇰 North Macedonia</h3>
                        </div>
                        <div class="country-card" data-country="norway">
                            <h3>🇳🇴 Norway</h3>
                        </div>
                        <div class="country-card" data-country="poland">
                            <h3>🇵🇱 Poland</h3>
                        </div>
                        <div class="country-card" data-country="portugal">
                            <h3>🇵🇹 Portugal</h3>
                        </div>
                        <div class="country-card" data-country="romania">
                            <h3>🇷🇴 Romania</h3>
                        </div>
                        <div class="country-card" data-country="serbia">
                            <h3>🇷🇸 Serbia</h3>
                        </div>
                        <div class="country-card" data-country="slovakia">
                            <h3>🇸🇰 Slovakia</h3>
                        </div>
                        <div class="country-card" data-country="slovenia">
                            <h3>🇸🇮 Slovenia</h3>
                        </div>
                        <div class="country-card" data-country="spain">
                            <h3>🇪🇸 Spain</h3>
                        </div>
                        <div class="country-card" data-country="sweden">
                            <h3>🇸🇪 Sweden</h3>
                        </div>
                        <div class="country-card" data-country="switzerland">
                            <h3>🇨🇭 Switzerland</h3>
                        </div>
                        <div class="country-card" data-country="turkey">
                            <h3>🇹🇷 Turkey</h3>
                        </div>
                    </div>
                </div>

                <!-- Country Detail View (Initially Hidden) -->
                <div id="country-detail" class="region-content" style="display: none;">
                    <div class="back-button">
                        <button id="back-to-europe">← Back to Europe</button>
                    </div>
                    <h2 id="country-detail-title">Country Universities</h2>
                    <p id="country-detail-description">Explore universities in this country.</p>

                    <!-- Sample university data for Germany -->
                    <div id="germany-universities" class="country-universities" style="display: none;">
                        <div class="city-section">
                            <div class="city-header">
                                <h3>Berlin</h3>
                            </div>
                            <div class="universities-grid">
                                <div class="university-card">
                                    <div class="university-logo">
                                        <img src="https://via.placeholder.com/80/1e88e5/ffffff?text=HU" alt="Humboldt">
                                    </div>
                                    <h4 class="university-name">Humboldt University of Berlin</h4>
                                    <div class="university-details">
                                        <p><span class="icon">🏆</span> QS Rank: 121</p>
                                        <p><span class="icon">📚</span> Programs: Arts, Humanities, Sciences</p>
                                        <p><span class="icon">💰</span> Tuition: €0 (Public)</p>
                                        <p><span class="icon">📅</span> Deadline: July 15, 2025</p>
                                    </div>
                                    <div class="university-tags">
                                        <span class="university-tag">IELTS: 6.5+</span>
                                        <span class="university-tag">TestDaF: 4</span>
                                    </div>
                                    <button class="view-details-btn">View Details</button>
                                </div>
                                <div class="university-card">
                                    <div class="university-logo">
                                        <img src="https://via.placeholder.com/80/1e88e5/ffffff?text=FU" alt="Free University">
                                    </div>
                                    <h4 class="university-name">Free University of Berlin</h4>
                                    <div class="university-details">
                                        <p><span class="icon">🏆</span> QS Rank: 130</p>
                                        <p><span class="icon">📚</span> Programs: Social Sciences, Natural Sciences</p>
                                        <p><span class="icon">💰</span> Tuition: €0 (Public)</p>
                                        <p><span class="icon">📅</span> Deadline: July 15, 2025</p>
                                    </div>
                                    <div class="university-tags">
                                        <span class="university-tag">IELTS: 6.5+</span>
                                        <span class="university-tag">TestDaF: 4</span>
                                    </div>
                                    <button class="view-details-btn">View Details</button>
                                </div>
                            </div>
                        </div>
                        <div class="city-section">
                            <div class="city-header">
                                <h3>Munich</h3>
                            </div>
                            <div class="universities-grid">
                                <div class="university-card">
                                    <div class="university-logo">
                                        <img src="https://via.placeholder.com/80/1e88e5/ffffff?text=TUM" alt="TUM">
                                    </div>
                                    <h4 class="university-name">Technical University of Munich</h4>
                                    <div class="university-details">
                                        <p><span class="icon">🏆</span> QS Rank: 50</p>
                                        <p><span class="icon">📚</span> Programs: Engineering, Computer Science</p>
                                        <p><span class="icon">💰</span> Tuition: €0 (Public)</p>
                                        <p><span class="icon">📅</span> Deadline: May 31, 2025</p>
                                    </div>
                                    <div class="university-tags">
                                        <span class="university-tag">IELTS: 6.5+</span>
                                        <span class="university-tag">TestDaF: 4</span>
                                    </div>
                                    <button class="view-details-btn">View Details</button>
                                </div>
                                <div class="university-card">
                                    <div class="university-logo">
                                        <img src="https://via.placeholder.com/80/1e88e5/ffffff?text=LMU" alt="LMU">
                                    </div>
                                    <h4 class="university-name">Ludwig Maximilian University of Munich</h4>
                                    <div class="university-details">
                                        <p><span class="icon">🏆</span> QS Rank: 63</p>
                                        <p><span class="icon">📚</span> Programs: Medicine, Law, Humanities</p>
                                        <p><span class="icon">💰</span> Tuition: €0 (Public)</p>
                                        <p><span class="icon">📅</span> Deadline: July 15, 2025</p>
                                    </div>
                                    <div class="university-tags">
                                        <span class="university-tag">IELTS: 6.5+</span>
                                        <span class="university-tag">TestDaF: 4</span>
                                    </div>
                                    <button class="view-details-btn">View Details</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sample university data for France -->
                    <div id="france-universities" class="country-universities" style="display: none;">
                        <div class="city-section">
                            <div class="city-header">
                                <h3>Paris</h3>
                            </div>
                            <div class="universities-grid">
                                <div class="university-card">
                                    <div class="university-logo">
                                        <img src="https://via.placeholder.com/80/1e88e5/ffffff?text=SU" alt="Sorbonne">
                                    </div>
                                    <h4 class="university-name">Sorbonne University</h4>
                                    <div class="university-details">
                                        <p><span class="icon">🏆</span> QS Rank: 83</p>
                                        <p><span class="icon">📚</span> Programs: Arts, Sciences, Medicine</p>
                                        <p><span class="icon">💰</span> Tuition: €170-3,770/year</p>
                                        <p><span class="icon">📅</span> Deadline: March 15, 2025</p>
                                    </div>
                                    <div class="university-tags">
                                        <span class="university-tag">IELTS: 6.5+</span>
                                        <span class="university-tag">DELF/DALF: C1</span>
                                    </div>
                                    <button class="view-details-btn">View Details</button>
                                </div>
                                <div class="university-card">
                                    <div class="university-logo">
                                        <img src="https://via.placeholder.com/80/1e88e5/ffffff?text=PSL" alt="PSL">
                                    </div>
                                    <h4 class="university-name">PSL University</h4>
                                    <div class="university-details">
                                        <p><span class="icon">🏆</span> QS Rank: 40</p>
                                        <p><span class="icon">📚</span> Programs: Sciences, Engineering, Arts</p>
                                        <p><span class="icon">💰</span> Tuition: €170-3,770/year</p>
                                        <p><span class="icon">📅</span> Deadline: April 30, 2025</p>
                                    </div>
                                    <div class="university-tags">
                                        <span class="university-tag">IELTS: 6.5+</span>
                                        <span class="university-tag">DELF/DALF: C1</span>
                                    </div>
                                    <button class="view-details-btn">View Details</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sample university data for Netherlands -->
                    <div id="netherlands-universities" class="country-universities" style="display: none;">
                        <div class="city-section">
                            <div class="city-header">
                                <h3>Amsterdam</h3>
                            </div>
                            <div class="universities-grid">
                                <div class="university-card">
                                    <div class="university-logo">
                                        <img src="https://via.placeholder.com/80/1e88e5/ffffff?text=UvA" alt="UvA">
                                    </div>
                                    <h4 class="university-name">University of Amsterdam</h4>
                                    <div class="university-details">
                                        <p><span class="icon">🏆</span> QS Rank: 55</p>
                                        <p><span class="icon">📚</span> Programs: Business, Social Sciences, Humanities</p>
                                        <p><span class="icon">💰</span> Tuition: €2,200 (EU), €15,000 (non-EU)</p>
                                        <p><span class="icon">📅</span> Deadline: April 1, 2025</p>
                                    </div>
                                    <div class="university-tags">
                                        <span class="university-tag">IELTS: 6.5+</span>
                                        <span class="university-tag">TOEFL: 92+</span>
                                    </div>
                                    <button class="view-details-btn">View Details</button>
                                </div>
                                <div class="university-card">
                                    <div class="university-logo">
                                        <img src="https://via.placeholder.com/80/1e88e5/ffffff?text=VU" alt="VU">
                                    </div>
                                    <h4 class="university-name">Vrije Universiteit Amsterdam</h4>
                                    <div class="university-details">
                                        <p><span class="icon">🏆</span> QS Rank: 209</p>
                                        <p><span class="icon">📚</span> Programs: Business, Psychology, Computer Science</p>
                                        <p><span class="icon">💰</span> Tuition: €2,200 (EU), €14,500 (non-EU)</p>
                                        <p><span class="icon">📅</span> Deadline: April 1, 2025</p>
                                    </div>
                                    <div class="university-tags">
                                        <span class="university-tag">IELTS: 6.5+</span>
                                        <span class="university-tag">TOEFL: 92+</span>
                                    </div>
                                    <button class="view-details-btn">View Details</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Default message for countries without sample data -->
                    <div id="default-universities" class="country-universities">
                        <div class="no-data-message">
                            <h3>Universities Coming Soon</h3>
                            <p>We're currently gathering information about universities in this country. Please check back later or contact our advisors for personalized assistance.</p>
                            <button class="contact-advisor-btn">Contact an Advisor</button>
                        </div>
                    </div>
                </div>

                <div class="why-study-abroad">
                    <h2>Why Study Abroad?</h2>
                    <p>Studying abroad offers numerous benefits for your academic and personal growth.</p>

                    <div class="benefits-grid">
                        <div class="benefit-card">
                            <div class="icon">🌍</div>
                            <h3>Global Perspective</h3>
                            <p>Gain a broader worldview and cultural understanding.</p>
                        </div>

                        <div class="benefit-card">
                            <div class="icon">💼</div>
                            <h3>Career Opportunities</h3>
                            <p>Enhance your resume and access international job markets.</p>
                        </div>

                        <div class="benefit-card">
                            <div class="icon">🗣️</div>
                            <h3>Language Skills</h3>
                            <p>Improve language proficiency through immersion.</p>
                        </div>

                        <div class="benefit-card">
                            <div class="icon">🎓</div>
                            <h3>Quality Education</h3>
                            <p>Access world-class teaching and research facilities.</p>
                        </div>
                    </div>
                </div>

                <footer>
                    <p>&copy; 2025 Go Abroad. All rights reserved.</p>
                </footer>
            </div>
        </div>
    </div>

    <script>
        // Region tab functionality
        document.querySelectorAll('.region-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Get the region ID
                const regionId = this.getAttribute('data-region');

                // Remove active class from all tabs
                document.querySelectorAll('.region-tab').forEach(t => {
                    t.classList.remove('active');
                });

                // Add active class to clicked tab
                this.classList.add('active');

                // Hide all region content
                document.querySelectorAll('.region-content').forEach(content => {
                    content.classList.remove('active');
                });

                // Show the selected region content
                document.getElementById(`${regionId}-region`).classList.add('active');

                // Hide country detail view if it's visible
                document.getElementById('country-detail').style.display = 'none';
            });
        });

        // Country card functionality
        document.querySelectorAll('.country-card').forEach(card => {
            card.addEventListener('click', function() {
                // Get the country ID
                const countryId = this.getAttribute('data-country');

                // Hide the Europe region content
                document.getElementById('europe-region').classList.remove('active');

                // Show the country detail view
                const countryDetail = document.getElementById('country-detail');
                countryDetail.style.display = 'block';

                // Update the country title and description
                document.getElementById('country-detail-title').textContent =
                    `${this.querySelector('h3').textContent} - Universities`;

                // Hide all country university sections
                document.querySelectorAll('.country-universities').forEach(section => {
                    section.style.display = 'none';
                });

                // Show the specific country universities if available, otherwise show default
                const countryUniversities = document.getElementById(`${countryId}-universities`);
                if (countryUniversities) {
                    countryUniversities.style.display = 'block';
                } else {
                    document.getElementById('default-universities').style.display = 'block';
                }
            });
        });

        // Back button functionality
        document.getElementById('back-to-europe').addEventListener('click', function() {
            // Hide country detail view
            document.getElementById('country-detail').style.display = 'none';

            // Show Europe region content
            document.getElementById('europe-region').classList.add('active');
        });

        // Country search functionality
        document.getElementById('country-search').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            document.querySelectorAll('.country-card').forEach(card => {
                const countryName = card.querySelector('h3').textContent.toLowerCase();

                if (countryName.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // Sidebar navigation functionality
        document.querySelectorAll('.sidebar-nav-item').forEach(item => {
            item.addEventListener('click', function() {
                // Remove active class from all items
                document.querySelectorAll('.sidebar-nav-item').forEach(i => {
                    i.classList.remove('active');
                });

                // Add active class to clicked item
                this.classList.add('active');
            });
        });

        // Initialize the UK region as active
        document.getElementById('uk-region').classList.add('active');
    </script>
</body>
</html>
