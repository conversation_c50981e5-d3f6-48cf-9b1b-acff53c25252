import React, { useState } from 'react';
import { FaSearch, FaGlobeAmericas, FaGraduationCap, FaMoneyBillWave, FaCalendarAlt } from 'react-icons/fa';

// Sample scholarship data
const scholarshipsData = [
  {
    id: 1,
    name: "Fulbright Foreign Student Program",
    country: "USA",
    amount: "Full tuition + stipend",
    deadline: "February 15, 2025",
    eligibility: "International students with excellent academic record",
    fields: ["All fields of study"],
    description: "The Fulbright Program offers grants to study, teach and conduct research for U.S. citizens to go abroad and for non-U.S. citizens to come to the United States.",
    link: "https://foreign.fulbrightonline.org/"
  },
  {
    id: 2,
    name: "Chevening Scholarships",
    country: "UK",
    amount: "Full tuition + living expenses",
    deadline: "November 2, 2024",
    eligibility: "International students with at least 2 years of work experience",
    fields: ["All fields of study"],
    description: "Chevening is the UK government's international awards program aimed at developing global leaders. Funded by the Foreign, Commonwealth and Development Office.",
    link: "https://www.chevening.org/"
  },
  {
    id: 3,
    name: "DAAD Scholarships",
    country: "Germany",
    amount: "€850 monthly + allowances",
    deadline: "October 15, 2024",
    eligibility: "International students with excellent academic record",
    fields: ["All fields of study"],
    description: "The German Academic Exchange Service (DAAD) offers scholarships for international students to study in Germany at various degree levels.",
    link: "https://www.daad.de/en/"
  },
  {
    id: 4,
    name: "Erasmus Mundus Joint Master Degrees",
    country: "Europe",
    amount: "€1,400 monthly + tuition",
    deadline: "Varies by program",
    eligibility: "International students with bachelor's degree",
    fields: ["Various fields"],
    description: "Erasmus Mundus Joint Master Degrees (EMJMDs) are prestigious, integrated, international study programs, jointly delivered by an international consortium of higher education institutions.",
    link: "https://ec.europa.eu/programmes/erasmus-plus/"
  },
  {
    id: 5,
    name: "Australia Awards Scholarships",
    country: "Australia",
    amount: "Full tuition + living expenses",
    deadline: "April 30, 2025",
    eligibility: "Citizens of participating countries in Asia, Africa, and the Pacific",
    fields: ["Development-related fields"],
    description: "Australia Awards Scholarships are long-term awards administered by the Department of Foreign Affairs and Trade. They aim to contribute to the development needs of Australia's partner countries.",
    link: "https://www.dfat.gov.au/people-to-people/australia-awards/Pages/australia-awards-scholarships"
  },
  {
    id: 6,
    name: "Vanier Canada Graduate Scholarships",
    country: "Canada",
    amount: "CAD $50,000 per year for 3 years",
    deadline: "November 3, 2024",
    eligibility: "International PhD students with leadership skills and high academic achievement",
    fields: ["All fields of study"],
    description: "The Vanier Canada Graduate Scholarships program helps Canadian institutions attract highly qualified doctoral students.",
    link: "https://vanier.gc.ca/en/home-accueil.html"
  }
];

const Scholarships = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCountry, setSelectedCountry] = useState('All');
  const [selectedField, setSelectedField] = useState('All');

  const countries = ['All', 'USA', 'UK', 'Germany', 'Europe', 'Australia', 'Canada'];
  const fields = ['All', 'Engineering', 'Business', 'Medicine', 'Arts', 'Sciences', 'Development-related fields'];

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleCountryChange = (e) => {
    setSelectedCountry(e.target.value);
  };

  const handleFieldChange = (e) => {
    setSelectedField(e.target.value);
  };

  // Filter scholarships based on search term, country, and field
  const filteredScholarships = scholarshipsData.filter(scholarship => {
    const matchesSearch = scholarship.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         scholarship.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCountry = selectedCountry === 'All' || scholarship.country === selectedCountry;
    
    const matchesField = selectedField === 'All' || 
                         scholarship.fields.some(field => field === selectedField || field === "All fields of study");
    
    return matchesSearch && matchesCountry && matchesField;
  });

  return (
    <div className="scholarships-container">
      <h1 className="page-title">Scholarships</h1>
      <p className="page-description">
        Discover scholarships and financial aid opportunities to fund your international education.
      </p>

      <div className="search-filter-container">
        <div className="search-container">
          <div className="search-input-wrapper">
            <FaSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search scholarships..."
              value={searchTerm}
              onChange={handleSearch}
              className="search-input"
            />
          </div>
        </div>

        <div className="filter-row">
          <div className="filter-group">
            <label htmlFor="country-filter">Country:</label>
            <select
              id="country-filter"
              value={selectedCountry}
              onChange={handleCountryChange}
              className="filter-select"
            >
              {countries.map(country => (
                <option key={country} value={country}>{country}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="field-filter">Field of Study:</label>
            <select
              id="field-filter"
              value={selectedField}
              onChange={handleFieldChange}
              className="filter-select"
            >
              {fields.map(field => (
                <option key={field} value={field}>{field}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="scholarships-grid">
        {filteredScholarships.length > 0 ? (
          filteredScholarships.map(scholarship => (
            <div key={scholarship.id} className="scholarship-card">
              <h3 className="scholarship-name">{scholarship.name}</h3>
              
              <div className="scholarship-detail">
                <FaGlobeAmericas className="scholarship-icon" />
                <span><strong>Country:</strong> {scholarship.country}</span>
              </div>
              
              <div className="scholarship-detail">
                <FaMoneyBillWave className="scholarship-icon" />
                <span><strong>Amount:</strong> {scholarship.amount}</span>
              </div>
              
              <div className="scholarship-detail">
                <FaCalendarAlt className="scholarship-icon" />
                <span><strong>Deadline:</strong> {scholarship.deadline}</span>
              </div>
              
              <div className="scholarship-detail">
                <FaGraduationCap className="scholarship-icon" />
                <span><strong>Eligibility:</strong> {scholarship.eligibility}</span>
              </div>
              
              <p className="scholarship-description">{scholarship.description}</p>
              
              <div className="scholarship-fields">
                {scholarship.fields.map((field, index) => (
                  <span key={index} className="scholarship-field">{field}</span>
                ))}
              </div>
              
              <a href={scholarship.link} target="_blank" rel="noopener noreferrer" className="scholarship-link">
                Apply Now
              </a>
            </div>
          ))
        ) : (
          <div className="no-results">
            <p>No scholarships found matching your criteria.</p>
          </div>
        )}
      </div>

      <div className="scholarship-tips">
        <h2>Scholarship Application Tips</h2>
        <div className="tips-grid">
          <div className="tip-card">
            <h3>Start Early</h3>
            <p>Begin your scholarship search and application process at least 12 months before your intended start date.</p>
          </div>
          <div className="tip-card">
            <h3>Prepare Documents</h3>
            <p>Have your academic transcripts, recommendation letters, and personal statements ready and polished.</p>
          </div>
          <div className="tip-card">
            <h3>Follow Instructions</h3>
            <p>Carefully read and follow all application instructions to avoid disqualification.</p>
          </div>
          <div className="tip-card">
            <h3>Highlight Achievements</h3>
            <p>Emphasize your academic achievements, leadership experiences, and community involvement.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Scholarships;
