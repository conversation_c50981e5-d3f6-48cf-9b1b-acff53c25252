<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scholarships - Go Abroad</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/modern-styles.css">
    <style>
        /* Page-specific styles */
        .page-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 50%, var(--accent) 100%);
            color: var(--white);
            padding: 6rem 0 3rem;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
            opacity: 0.4;
            z-index: 0;
        }

        .page-header-content {
            position: relative;
            z-index: 1;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(to right, var(--white), rgba(255, 255, 255, 0.8));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            display: inline-block;
        }

        .page-description {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            max-width: 700px;
        }

        .scholarship-card {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all var(--transition-normal) var(--transition-ease);
        }

        .scholarship-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .scholarship-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .scholarship-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-900);
        }

        .scholarship-amount {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: var(--white);
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-full);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .scholarship-details {
            margin-bottom: 1rem;
        }

        .scholarship-detail {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            color: var(--gray-700);
            font-size: 0.9rem;
        }

        .scholarship-description {
            color: var(--gray-600);
            margin-bottom: 1rem;
            font-size: 0.95rem;
        }

        .scholarship-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .scholarship-deadline {
            color: var(--error);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .filter-container {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .filter-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--gray-900);
        }

        .filter-group {
            margin-bottom: 1rem;
        }

        .filter-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: var(--gray-700);
        }

        .badge-custom {
            background-color: var(--gray-100);
            color: var(--primary);
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-full);
            font-size: 0.8rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            display: inline-block;
        }

        .tips-section {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: 2rem;
            margin-top: 3rem;
        }

        .tip-item {
            margin-bottom: 0.75rem;
            padding-left: 1.5rem;
            position: relative;
            color: var(--gray-700);
        }

        .tip-item:before {
            content: '💡';
            position: absolute;
            left: 0;
            top: 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top bg-white">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-globe-americas"></i> Go Abroad
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="search_universities.html">Search Universities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="scholarships.html">Scholarships</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="applications.html">Applications</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admission_guide.html">Admission Guide</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <header class="page-header">
        <div class="container">
            <div class="page-header-content" data-aos="fade-up">
                <h1 class="page-title">Scholarships & Financial Aid</h1>
                <p class="page-description">Discover funding opportunities to make your international education dreams affordable.</p>
            </div>
        </div>
    </header>

    <main>
        <div class="container py-5">
            <div class="row">
                <!-- Filters Sidebar -->
                <div class="col-lg-3" data-aos="fade-up">
                    <div class="filter-container">
                        <h3 class="filter-title">Filter Scholarships</h3>

                        <div class="filter-group">
                            <label class="filter-label" for="countryFilter">Country</label>
                            <select class="form-select" id="countryFilter">
                                <option value="">All Countries</option>
                                <option value="uk">United Kingdom</option>
                                <option value="usa">United States</option>
                                <option value="canada">Canada</option>
                                <option value="germany">Germany</option>
                                <option value="australia">Australia</option>
                                <option value="netherlands">Netherlands</option>
                                <option value="sweden">Sweden</option>
                                <option value="france">France</option>
                                <option value="europe">Europe</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label" for="typeFilter">Scholarship Type</label>
                            <select class="form-select" id="typeFilter">
                                <option value="">All Types</option>
                                <option value="merit">Merit-based</option>
                                <option value="need">Need-based</option>
                                <option value="government">Government</option>
                                <option value="university">University-specific</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label" for="amountFilter">Funding Amount</label>
                            <select class="form-select" id="amountFilter">
                                <option value="">All Amounts</option>
                                <option value="partial">Partial Funding</option>
                                <option value="full">Full Funding</option>
                                <option value="living">Living Expenses</option>
                            </select>
                        </div>

                        <button class="btn btn-primary w-100 mt-3">Apply Filters</button>
                    </div>
                </div>

                <!-- Scholarships List -->
                <div class="col-lg-9" id="scholarshipsList">
                    <!-- Scholarships will be populated by JavaScript -->
                </div>
            </div>

            <!-- How to Apply Section -->
            <div class="tips-section mt-5" data-aos="fade-up">
                <div class="section-header mb-4">
                    <h2 class="section-title">How to Apply for Scholarships</h2>
                    <p class="section-subtitle">Follow these steps to maximize your chances of securing funding</p>
                </div>

                <div class="row">
                    <div class="col-md-6" data-aos="fade-up" data-aos-delay="100">
                        <h4 class="mb-3">Step-by-Step Process</h4>
                        <ol class="process-list">
                            <li><strong>Research Early:</strong> Start looking 12-18 months before your intended start date</li>
                            <li><strong>Check Eligibility:</strong> Verify GPA, nationality, and program requirements</li>
                            <li><strong>Prepare Documents:</strong> Gather transcripts, test scores, and references</li>
                            <li><strong>Write Essays:</strong> Craft compelling personal statements and essays</li>
                            <li><strong>Submit Applications:</strong> Apply before deadlines with complete documentation</li>
                            <li><strong>Follow Up:</strong> Track application status and respond to requests</li>
                        </ol>
                    </div>
                    <div class="col-md-6" data-aos="fade-up" data-aos-delay="200">
                        <h4 class="mb-3">Documents Typically Required</h4>
                        <div class="tip-item">Academic transcripts and certificates</div>
                        <div class="tip-item">IELTS/TOEFL/PTE test scores</div>
                        <div class="tip-item">Statement of Purpose (SOP)</div>
                        <div class="tip-item">Letters of Recommendation (LOR)</div>
                        <div class="tip-item">CV/Resume</div>
                        <div class="tip-item">Financial statements (for need-based)</div>
                        <div class="tip-item">Portfolio (for creative fields)</div>
                        <div class="tip-item">Research proposal (for PhD)</div>
                    </div>
                </div>
            </div>

            <!-- Tips Section -->
            <div class="tips-section mt-5" data-aos="fade-up">
                <div class="section-header mb-4">
                    <h2 class="section-title">Pro Tips for Scholarship Success</h2>
                    <p class="section-subtitle">Expert advice to help you stand out from other applicants</p>
                </div>

                <div class="row">
                    <div class="col-md-6" data-aos="fade-up" data-aos-delay="100">
                        <h4 class="mb-3">Writing a Strong SOP</h4>
                        <div class="tip-item">Start with a compelling hook</div>
                        <div class="tip-item">Clearly state your goals and motivations</div>
                        <div class="tip-item">Highlight relevant experiences and achievements</div>
                        <div class="tip-item">Explain how the scholarship aligns with your plans</div>
                        <div class="tip-item">Keep it concise and well-structured</div>
                    </div>
                    <div class="col-md-6" data-aos="fade-up" data-aos-delay="200">
                        <h4 class="mb-3">Getting Strong Reference Letters</h4>
                        <div class="tip-item">Choose referees who know you well</div>
                        <div class="tip-item">Provide them with your achievements list</div>
                        <div class="tip-item">Give at least 4-6 weeks notice</div>
                        <div class="tip-item">Follow up politely before deadlines</div>
                        <div class="tip-item">Thank them after submission</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="footer-brand">
                        <i class="fas fa-globe-americas"></i> Go Abroad
                    </div>
                    <p class="footer-text">Your trusted partner for international education. We help students achieve their dreams of studying abroad.</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Quick Links</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="index.html">Home</a></li>
                        <li class="footer-link"><a href="search_universities.html">Universities</a></li>
                        <li class="footer-link"><a href="scholarships.html">Scholarships</a></li>
                        <li class="footer-link"><a href="applications.html">Applications</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Destinations</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="#">United Kingdom</a></li>
                        <li class="footer-link"><a href="#">United States</a></li>
                        <li class="footer-link"><a href="#">Canada</a></li>
                        <li class="footer-link"><a href="#">Australia</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Contact</h3>
                    <ul class="footer-links">
                        <li class="footer-contact"><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li class="footer-contact"><i class="fas fa-phone me-2"></i> +****************</li>
                        <li class="footer-contact"><i class="fas fa-map-marker-alt me-2"></i> New York, NY</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Go Abroad. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Scholarship data
        const scholarships = [
            {
                name: "Chevening Scholarships",
                country: "uk",
                type: "government",
                amount: "Full Funding",
                deadline: "November 2, 2025",
                eligibility: ["Bachelor's degree", "2+ years work experience", "IELTS 6.5+", "Leadership potential"],
                description: "UK government's global scholarship programme for outstanding emerging leaders.",
                link: "https://www.chevening.org/",
                funding: "Tuition fees, living allowance, travel costs"
            },
            {
                name: "Fulbright Foreign Student Program",
                country: "usa",
                type: "government",
                amount: "Full Funding",
                deadline: "October 15, 2025",
                eligibility: ["Bachelor's degree", "Strong academic record", "TOEFL 80+/IELTS 6.5+", "Leadership experience"],
                description: "Provides funding for graduate study, research, and teaching in the United States.",
                link: "https://foreign.fulbrightonline.org/",
                funding: "Tuition, living stipend, health insurance, travel"
            },
            {
                name: "DAAD Scholarships",
                country: "germany",
                type: "government",
                amount: "Partial Funding",
                deadline: "October 31, 2025",
                eligibility: ["Bachelor's degree", "German language skills (some programs)", "Academic excellence"],
                description: "German Academic Exchange Service scholarships for international students.",
                link: "https://www.daad.de/",
                funding: "€850-1,200/month + health insurance"
            },
            {
                name: "Vanier Canada Graduate Scholarships",
                country: "canada",
                type: "government",
                amount: "Full Funding",
                deadline: "November 1, 2025",
                eligibility: ["PhD program", "Canadian citizen/PR or international", "Academic excellence", "Research potential"],
                description: "Prestigious scholarship for doctoral students demonstrating leadership skills.",
                link: "https://vanier.gc.ca/",
                funding: "CAD $50,000/year for 3 years"
            },
            {
                name: "Australia Awards Scholarships",
                country: "australia",
                type: "government",
                amount: "Full Funding",
                deadline: "April 30, 2025",
                eligibility: ["Developing country citizen", "Bachelor's degree", "IELTS 6.5+", "Work experience"],
                description: "Australian government scholarships for students from developing countries.",
                link: "https://www.australiaawards.gov.au/",
                funding: "Tuition, living allowance, health cover, travel"
            },
            {
                name: "Holland Scholarship",
                country: "netherlands",
                type: "government",
                amount: "Partial Funding",
                deadline: "February 1, 2025",
                eligibility: ["Non-EU/EEA nationality", "Bachelor's/Master's program", "First-time in Netherlands"],
                description: "Scholarship for international students studying in the Netherlands.",
                link: "https://www.studyinholland.nl/",
                funding: "€5,000 for first year"
            },
            {
                name: "Swedish Institute Scholarships",
                country: "sweden",
                type: "government",
                amount: "Full Funding",
                deadline: "February 10, 2025",
                eligibility: ["Developing country citizen", "Master's program", "IELTS 6.5+", "Leadership potential"],
                description: "Scholarships for global professionals to pursue Master's studies in Sweden.",
                link: "https://si.se/",
                funding: "Tuition, living allowance, travel grant, insurance"
            },
            {
                name: "Eiffel Excellence Scholarship",
                country: "france",
                type: "government",
                amount: "Partial Funding",
                deadline: "January 8, 2025",
                eligibility: ["Master's/PhD program", "Non-French nationality", "Age limit: 30 (Master's), 35 (PhD)"],
                description: "French government scholarship for international students in priority fields.",
                link: "https://www.campusfrance.org/",
                funding: "€1,181/month (Master's), €1,700/month (PhD)"
            },
            {
                name: "Rhodes Scholarships",
                country: "uk",
                type: "merit",
                amount: "Full Funding",
                deadline: "October 1, 2025",
                eligibility: ["Bachelor's degree", "Age 18-24", "Academic excellence", "Leadership", "Service to others"],
                description: "World's oldest international scholarship programme at University of Oxford.",
                link: "https://www.rhodeshouse.ox.ac.uk/",
                funding: "University fees, living stipend, travel costs"
            },
            {
                name: "Gates Cambridge Scholarships",
                country: "uk",
                type: "merit",
                amount: "Full Funding",
                deadline: "December 3, 2025",
                eligibility: ["Non-UK citizen", "Postgraduate program", "Academic excellence", "Leadership potential"],
                description: "Prestigious scholarship for outstanding students at University of Cambridge.",
                link: "https://www.gatescambridge.org/",
                funding: "University composition fee, maintenance allowance, travel"
            },
            {
                name: "Erasmus Mundus Joint Master Degrees",
                country: "europe",
                type: "university",
                amount: "Full Funding",
                deadline: "January 15, 2025",
                eligibility: ["Bachelor's degree", "English proficiency", "Varies by program"],
                description: "EU-funded scholarships for joint Master's programmes across European universities.",
                link: "https://ec.europa.eu/programmes/erasmus-plus/",
                funding: "€1,400/month + travel and installation costs"
            },
            {
                name: "University of Melbourne Graduate Scholarships",
                country: "australia",
                type: "university",
                amount: "Partial Funding",
                deadline: "October 31, 2025",
                eligibility: ["International student", "Research degree", "Academic merit"],
                description: "Merit-based scholarships for international research students.",
                link: "https://scholarships.unimelb.edu.au/",
                funding: "Up to AUD $31,000/year"
            }
        ];

        // Function to render scholarships
        function renderScholarships(scholarshipsToShow = scholarships) {
            const container = document.getElementById('scholarshipsList');
            container.innerHTML = '';

            if (scholarshipsToShow.length === 0) {
                container.innerHTML = '<div class="col-12"><div class="alert alert-info">No scholarships found matching your criteria.</div></div>';
                return;
            }

            scholarshipsToShow.forEach((scholarship, index) => {
                const card = document.createElement('div');
                card.className = 'col-md-6 col-lg-6 mb-4';
                card.setAttribute('data-aos', 'fade-up');
                card.setAttribute('data-aos-delay', (index % 3) * 100);

                const countryFlags = {
                    'uk': '🇬🇧',
                    'usa': '🇺🇸',
                    'canada': '🇨🇦',
                    'germany': '🇩🇪',
                    'australia': '🇦🇺',
                    'netherlands': '🇳🇱',
                    'sweden': '🇸🇪',
                    'france': '🇫🇷',
                    'europe': '🇪🇺'
                };

                card.innerHTML = `
                    <div class="scholarship-card">
                        <div class="scholarship-header">
                            <h3 class="scholarship-title">${scholarship.name}</h3>
                            <span class="scholarship-amount">${scholarship.amount}</span>
                        </div>
                        <div class="scholarship-details">
                            <div class="scholarship-detail">
                                <i class="fas fa-globe text-primary"></i>
                                <span>${countryFlags[scholarship.country] || '🌍'} ${scholarship.country.toUpperCase()}</span>
                            </div>
                            <div class="scholarship-detail">
                                <i class="fas fa-graduation-cap text-primary"></i>
                                <span>${scholarship.type.charAt(0).toUpperCase() + scholarship.type.slice(1)} Scholarship</span>
                            </div>
                            <div class="scholarship-detail">
                                <i class="fas fa-money-bill-wave text-primary"></i>
                                <span>${scholarship.funding}</span>
                            </div>
                        </div>
                        <p class="scholarship-description">${scholarship.description}</p>
                        <div class="mb-3">
                            <strong>Eligibility:</strong><br>
                            ${scholarship.eligibility.map(req => `<span class="badge-custom">${req}</span>`).join('')}
                        </div>
                        <div class="scholarship-footer">
                            <span class="scholarship-deadline"><i class="fas fa-clock me-1"></i> Deadline: ${scholarship.deadline}</span>
                            <a href="${scholarship.link}" target="_blank" class="btn btn-primary">Apply Now</a>
                        </div>
                    </div>
                `;

                container.appendChild(card);
            });
        }

        // Filter functionality
        function filterScholarships() {
            const countryFilter = document.getElementById('countryFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const amountFilter = document.getElementById('amountFilter').value;

            let filtered = scholarships.filter(scholarship => {
                const matchesCountry = !countryFilter || scholarship.country === countryFilter;
                const matchesType = !typeFilter || scholarship.type === typeFilter;
                const matchesAmount = !amountFilter ||
                    (amountFilter === 'full' && scholarship.amount.includes('Full')) ||
                    (amountFilter === 'partial' && scholarship.amount.includes('Partial')) ||
                    (amountFilter === 'living' && scholarship.funding.toLowerCase().includes('living'));

                return matchesCountry && matchesType && matchesAmount;
            });

            renderScholarships(filtered);
        }

        // Handle navigation active states
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

            // Set active link based on current page
            const currentPage = window.location.pathname.split('/').pop() || 'index.html';
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if ((currentPage === 'index.html' && (href === '#' || href === 'index.html')) ||
                    (href === currentPage)) {
                    link.classList.add('active');
                }
            });

            // Event listeners for filters
            document.getElementById('countryFilter').addEventListener('change', filterScholarships);
            document.getElementById('typeFilter').addEventListener('change', filterScholarships);
            document.getElementById('amountFilter').addEventListener('change', filterScholarships);

            // Mobile menu toggle
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            if (navbarToggler) {
                navbarToggler.addEventListener('click', function() {
                    navbarCollapse.classList.toggle('show');
                });
            }

            // Initial render
            renderScholarships();
        });
    </script>
</body>
</html>
