<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scholarships - Go Abroad</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/modern-styles.css">
    <style>
        /* Modern Banner Section */
        .page-header {
            position: relative;
            height: 400px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 140px; /* Account for sticky headers */
            border-radius: 20px;
            margin-bottom: 2rem;
        }

        .page-header-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('https://images.unsplash.com/photo-*************-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080&q=80');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            filter: blur(3px);
            transform: scale(1.1);
            z-index: 1;
        }

        .page-header-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(0, 123, 255, 0.8) 0%,
                rgba(127, 0, 255, 0.8) 50%,
                rgba(0, 201, 167, 0.8) 100%);
            z-index: 2;
        }

        .page-header-content {
            position: relative;
            z-index: 3;
            text-align: center;
            color: white;
            max-width: 800px;
            padding: 0 2rem;
        }

        .page-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            letter-spacing: -1px;
        }

        .page-description {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.95);
            margin-bottom: 2rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1.6;
        }

        /* Hanging Photo Elements */
        .hanging-photos {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 4;
            display: flex;
            justify-content: space-between;
            pointer-events: none;
        }

        .hanging-photo {
            width: 80px;
            height: 60px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            transform: rotate(-5deg);
            animation: float 3s ease-in-out infinite;
        }

        .hanging-photo:nth-child(2) {
            transform: rotate(3deg);
            animation-delay: -1s;
        }

        .hanging-photo:nth-child(3) {
            transform: rotate(-2deg);
            animation-delay: -2s;
        }

        .hanging-photo:nth-child(4) {
            transform: rotate(4deg);
            animation-delay: -0.5s;
        }

        .hanging-photo:nth-child(5) {
            transform: rotate(-3deg);
            animation-delay: -1.5s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(var(--rotation, -5deg)); }
            50% { transform: translateY(-10px) rotate(var(--rotation, -5deg)); }
        }

        .scholarship-card {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all var(--transition-normal) var(--transition-ease);
        }

        .scholarship-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .scholarship-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .scholarship-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-900);
        }

        .scholarship-amount {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: var(--white);
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-full);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .scholarship-details {
            margin-bottom: 1rem;
        }

        .scholarship-detail {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            color: var(--gray-700);
            font-size: 0.9rem;
        }

        .scholarship-description {
            color: var(--gray-600);
            margin-bottom: 1rem;
            font-size: 0.95rem;
        }

        .scholarship-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .scholarship-deadline {
            color: var(--error);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .filter-container {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .filter-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--gray-900);
        }

        .filter-group {
            margin-bottom: 1rem;
        }

        .filter-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: var(--gray-700);
        }

        .badge-custom {
            background-color: var(--gray-100);
            color: var(--primary);
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-full);
            font-size: 0.8rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            display: inline-block;
        }

        .tips-section {
            background-color: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: 2rem;
            margin-top: 3rem;
        }

        .tip-item {
            margin-bottom: 0.75rem;
            padding-left: 1.5rem;
            position: relative;
            color: var(--gray-700);
        }

        .tip-item:before {
            content: '💡';
            position: absolute;
            left: 0;
            top: 0;
        }

        /* Header Banner Styles - Sticky */
        .header-banner {
            background: linear-gradient(135deg, #0066CC 0%, #00c9a7 100%);
            padding: 10px 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            overflow: hidden;
            z-index: 1001;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .header-banner-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            margin: 0;
            padding: 0 20px;
        }

        .scholarship-banner {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px 20px;
            border-radius: 25px;
            color: #0066CC;
            font-weight: 700;
            font-size: 1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .scholarship-text {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .scholarship-text i {
            color: #FFD700;
            font-size: 1.2rem;
        }

        .scholarship-cta {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        /* Expanded Country Flags Section */
        .country-rotator {
            flex: 1;
            height: 70px;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 35px;
            overflow: hidden;
            margin-left: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        /* Horizontal Scrolling Countries */
        .countries-track {
            display: flex;
            height: 100%;
            animation: scrollCountries 60s linear infinite;
            gap: 25px;
            padding: 0 25px;
            align-items: center;
        }

        .country-item {
            display: flex;
            align-items: center;
            gap: 15px;
            min-width: 180px;
            height: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 10px 15px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
        }

        .country-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .country-flag-single {
            width: 60px;
            height: 45px;
            border-radius: 10px;
            object-fit: contain;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
            border: 3px solid rgba(255, 255, 255, 0.5);
            background: white;
            flex-shrink: 0;
        }

        .country-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
            flex: 1;
        }

        .country-code {
            color: white;
            font-weight: 900;
            font-size: 1.4rem;
            text-shadow: 0 2px 6px rgba(0, 0, 0, 0.7);
            line-height: 1;
            letter-spacing: 0.8px;
        }

        .country-full-name {
            color: rgba(255, 255, 255, 0.95);
            font-size: 1rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            line-height: 1;
        }

        /* Scrolling Animation */
        @keyframes scrollCountries {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        /* Pause animation on hover */
        .country-rotator:hover .countries-track {
            animation-play-state: paused;
        }

        /* Modern Logo Styles */
        .modern-logo {
            text-decoration: none !important;
            transition: all 0.3s ease;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            border-radius: 25px;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 201, 167, 0.1));
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .logo-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #007bff, #00c9a7);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .modern-logo:hover .logo-container {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
            border-color: rgba(0, 123, 255, 0.3);
        }

        .modern-logo:hover .logo-container::before {
            opacity: 0.1;
        }

        .globe-icon {
            position: relative;
            font-size: 1.8rem;
            color: #007bff;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 201, 167, 0.1));
        }

        .flight-path {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            border: 2px dashed rgba(0, 201, 167, 0.4);
            border-radius: 50%;
            opacity: 0;
            transition: all 0.3s ease;
            animation: rotatePath 8s linear infinite;
        }

        .modern-logo:hover .flight-path {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.2);
        }

        .modern-logo:hover .globe-icon {
            color: #00c9a7;
            transform: scale(1.1);
            animation: globePulse 2s ease-in-out infinite;
        }

        .logo-text {
            font-family: 'Inter', 'Poppins', 'Montserrat', sans-serif;
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #007bff, #00c9a7);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            letter-spacing: -0.5px;
        }

        .modern-logo:hover .logo-text {
            transform: translateX(3px);
            filter: drop-shadow(0 4px 8px rgba(0, 123, 255, 0.3));
        }

        @keyframes rotatePath {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        @keyframes globePulse {
            0%, 100% { transform: scale(1.1); }
            50% { transform: scale(1.2); }
        }
    </style>
</head>
<body>
    <!-- Header Banner -->
    <div class="header-banner">
        <div class="header-banner-content">
            <div class="scholarship-banner">
                <div class="scholarship-text">
                    <i class="fas fa-graduation-cap"></i>
                    <span>Scholarships Available – Apply Now!</span>
                </div>
                <a href="scholarships.html" class="scholarship-cta">Apply Now</a>
            </div>

            <!-- Horizontal Scrolling Country Flags -->
            <div class="country-rotator">
                <div class="countries-track" id="countriesTrack">
                    <!-- Countries will be dynamically inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation - Sticky Below Header -->
    <nav class="navbar navbar-expand-lg fixed-top bg-white" style="top: 90px; z-index: 1000; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px);">
        <div class="container">
            <a class="navbar-brand modern-logo" href="index.html">
                <div class="logo-container">
                    <div class="globe-icon">
                        <i class="fas fa-globe-americas"></i>
                        <div class="flight-path"></div>
                    </div>
                    <span class="logo-text">Go Abroad</span>
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="search_universities.html">Search Universities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="scholarships.html">Scholarships</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="applications.html">Applications</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admission_guide.html">Admission Guide</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <header class="page-header">
        <div class="page-header-bg"></div>
        <div class="page-header-overlay"></div>

        <!-- Hanging Photos -->
        <div class="hanging-photos">
            <div class="hanging-photo" style="--rotation: -5deg;">💰</div>
            <div class="hanging-photo" style="--rotation: 3deg;">🎓</div>
            <div class="hanging-photo" style="--rotation: -2deg;">📚</div>
            <div class="hanging-photo" style="--rotation: 4deg;">🏆</div>
            <div class="hanging-photo" style="--rotation: -3deg;">💡</div>
        </div>

        <div class="container">
            <div class="page-header-content" data-aos="fade-up">
                <h1 class="page-title">Go Abroad</h1>
                <p class="page-description">Discover scholarship opportunities to fund your international education journey.</p>
            </div>
        </div>
    </header>

    <main>
        <div class="container py-5">
            <div class="row">
                <!-- Filters Sidebar -->
                <div class="col-lg-3" data-aos="fade-up">
                    <div class="filter-container">
                        <h3 class="filter-title">Filter Scholarships</h3>

                        <div class="filter-group">
                            <label class="filter-label" for="countryFilter">Country</label>
                            <select class="form-select" id="countryFilter">
                                <option value="">All Countries</option>
                                <option value="uk">United Kingdom</option>
                                <option value="usa">United States</option>
                                <option value="canada">Canada</option>
                                <option value="germany">Germany</option>
                                <option value="australia">Australia</option>
                                <option value="netherlands">Netherlands</option>
                                <option value="sweden">Sweden</option>
                                <option value="france">France</option>
                                <option value="europe">Europe</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label" for="typeFilter">Scholarship Type</label>
                            <select class="form-select" id="typeFilter">
                                <option value="">All Types</option>
                                <option value="merit">Merit-based</option>
                                <option value="need">Need-based</option>
                                <option value="government">Government</option>
                                <option value="university">University-specific</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label" for="amountFilter">Funding Amount</label>
                            <select class="form-select" id="amountFilter">
                                <option value="">All Amounts</option>
                                <option value="partial">Partial Funding</option>
                                <option value="full">Full Funding</option>
                                <option value="living">Living Expenses</option>
                            </select>
                        </div>

                        <button class="btn btn-primary w-100 mt-3">Apply Filters</button>
                    </div>
                </div>

                <!-- Scholarships List -->
                <div class="col-lg-9" id="scholarshipsList">
                    <!-- Scholarships will be populated by JavaScript -->
                </div>
            </div>

            <!-- How to Apply Section -->
            <div class="tips-section mt-5" data-aos="fade-up">
                <div class="section-header mb-4">
                    <h2 class="section-title">How to Apply for Scholarships</h2>
                    <p class="section-subtitle">Follow these steps to maximize your chances of securing funding</p>
                </div>

                <div class="row">
                    <div class="col-md-6" data-aos="fade-up" data-aos-delay="100">
                        <h4 class="mb-3">Step-by-Step Process</h4>
                        <ol class="process-list">
                            <li><strong>Research Early:</strong> Start looking 12-18 months before your intended start date</li>
                            <li><strong>Check Eligibility:</strong> Verify GPA, nationality, and program requirements</li>
                            <li><strong>Prepare Documents:</strong> Gather transcripts, test scores, and references</li>
                            <li><strong>Write Essays:</strong> Craft compelling personal statements and essays</li>
                            <li><strong>Submit Applications:</strong> Apply before deadlines with complete documentation</li>
                            <li><strong>Follow Up:</strong> Track application status and respond to requests</li>
                        </ol>
                    </div>
                    <div class="col-md-6" data-aos="fade-up" data-aos-delay="200">
                        <h4 class="mb-3">Documents Typically Required</h4>
                        <div class="tip-item">Academic transcripts and certificates</div>
                        <div class="tip-item">IELTS/TOEFL/PTE test scores</div>
                        <div class="tip-item">Statement of Purpose (SOP)</div>
                        <div class="tip-item">Letters of Recommendation (LOR)</div>
                        <div class="tip-item">CV/Resume</div>
                        <div class="tip-item">Financial statements (for need-based)</div>
                        <div class="tip-item">Portfolio (for creative fields)</div>
                        <div class="tip-item">Research proposal (for PhD)</div>
                    </div>
                </div>
            </div>

            <!-- Tips Section -->
            <div class="tips-section mt-5" data-aos="fade-up">
                <div class="section-header mb-4">
                    <h2 class="section-title">Pro Tips for Scholarship Success</h2>
                    <p class="section-subtitle">Expert advice to help you stand out from other applicants</p>
                </div>

                <div class="row">
                    <div class="col-md-6" data-aos="fade-up" data-aos-delay="100">
                        <h4 class="mb-3">Writing a Strong SOP</h4>
                        <div class="tip-item">Start with a compelling hook</div>
                        <div class="tip-item">Clearly state your goals and motivations</div>
                        <div class="tip-item">Highlight relevant experiences and achievements</div>
                        <div class="tip-item">Explain how the scholarship aligns with your plans</div>
                        <div class="tip-item">Keep it concise and well-structured</div>
                    </div>
                    <div class="col-md-6" data-aos="fade-up" data-aos-delay="200">
                        <h4 class="mb-3">Getting Strong Reference Letters</h4>
                        <div class="tip-item">Choose referees who know you well</div>
                        <div class="tip-item">Provide them with your achievements list</div>
                        <div class="tip-item">Give at least 4-6 weeks notice</div>
                        <div class="tip-item">Follow up politely before deadlines</div>
                        <div class="tip-item">Thank them after submission</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="footer-brand">
                        <i class="fas fa-globe-americas"></i> Go Abroad
                    </div>
                    <p class="footer-text">Your trusted partner for international education. We help students achieve their dreams of studying abroad.</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Quick Links</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="index.html">Home</a></li>
                        <li class="footer-link"><a href="search_universities.html">Universities</a></li>
                        <li class="footer-link"><a href="scholarships.html">Scholarships</a></li>
                        <li class="footer-link"><a href="applications.html">Applications</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Destinations</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="#">United Kingdom</a></li>
                        <li class="footer-link"><a href="#">United States</a></li>
                        <li class="footer-link"><a href="#">Canada</a></li>
                        <li class="footer-link"><a href="#">Australia</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Contact</h3>
                    <ul class="footer-links">
                        <li class="footer-contact"><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li class="footer-contact"><i class="fas fa-phone me-2"></i> +****************</li>
                        <li class="footer-contact"><i class="fas fa-map-marker-alt me-2"></i> New York, NY</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Go Abroad. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <script>
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Horizontal Scrolling Countries Functionality
        function initializeCountryRotator() {
            const countries = [
                // Europe
                { code: 'UK', name: 'United Kingdom', flag: 'https://flagcdn.com/w320/gb.png', region: 'europe' },
                { code: 'GER', name: 'Germany', flag: 'https://flagcdn.com/w320/de.png', region: 'europe' },
                { code: 'FRA', name: 'France', flag: 'https://flagcdn.com/w320/fr.png', region: 'europe' },
                { code: 'ITA', name: 'Italy', flag: 'https://flagcdn.com/w320/it.png', region: 'europe' },
                { code: 'ESP', name: 'Spain', flag: 'https://flagcdn.com/w320/es.png', region: 'europe' },
                { code: 'NLD', name: 'Netherlands', flag: 'https://flagcdn.com/w320/nl.png', region: 'europe' },
                { code: 'SWE', name: 'Sweden', flag: 'https://flagcdn.com/w320/se.png', region: 'europe' },
                { code: 'CHE', name: 'Switzerland', flag: 'https://flagcdn.com/w320/ch.png', region: 'europe' },
                { code: 'NOR', name: 'Norway', flag: 'https://flagcdn.com/w320/no.png', region: 'europe' },
                { code: 'DNK', name: 'Denmark', flag: 'https://flagcdn.com/w320/dk.png', region: 'europe' },
                { code: 'FIN', name: 'Finland', flag: 'https://flagcdn.com/w320/fi.png', region: 'europe' },
                { code: 'AUT', name: 'Austria', flag: 'https://flagcdn.com/w320/at.png', region: 'europe' },
                { code: 'BEL', name: 'Belgium', flag: 'https://flagcdn.com/w320/be.png', region: 'europe' },
                { code: 'IRL', name: 'Ireland', flag: 'https://flagcdn.com/w320/ie.png', region: 'europe' },
                { code: 'POL', name: 'Poland', flag: 'https://flagcdn.com/w320/pl.png', region: 'europe' },
                { code: 'PRT', name: 'Portugal', flag: 'https://flagcdn.com/w320/pt.png', region: 'europe' },
                { code: 'GRC', name: 'Greece', flag: 'https://flagcdn.com/w320/gr.png', region: 'europe' },
                { code: 'CZE', name: 'Czech Republic', flag: 'https://flagcdn.com/w320/cz.png', region: 'europe' },
                { code: 'HUN', name: 'Hungary', flag: 'https://flagcdn.com/w320/hu.png', region: 'europe' },

                // North America
                { code: 'USA', name: 'United States', flag: 'https://flagcdn.com/w320/us.png', region: 'usa' },
                { code: 'CAN', name: 'Canada', flag: 'https://flagcdn.com/w320/ca.png', region: 'canada' },

                // Oceania
                { code: 'AUS', name: 'Australia', flag: 'https://flagcdn.com/w320/au.png', region: 'australia' },
                { code: 'NZL', name: 'New Zealand', flag: 'https://flagcdn.com/w320/nz.png', region: 'australia' },

                // Asia
                { code: 'JPN', name: 'Japan', flag: 'https://flagcdn.com/w320/jp.png', region: 'asia' },
                { code: 'KOR', name: 'South Korea', flag: 'https://flagcdn.com/w320/kr.png', region: 'asia' },
                { code: 'SGP', name: 'Singapore', flag: 'https://flagcdn.com/w320/sg.png', region: 'asia' },
                { code: 'HKG', name: 'Hong Kong', flag: 'https://flagcdn.com/w320/hk.png', region: 'asia' },
                { code: 'CHN', name: 'China', flag: 'https://flagcdn.com/w320/cn.png', region: 'asia' },
                { code: 'MYS', name: 'Malaysia', flag: 'https://flagcdn.com/w320/my.png', region: 'asia' },
                { code: 'THA', name: 'Thailand', flag: 'https://flagcdn.com/w320/th.png', region: 'asia' },

                // Middle East
                { code: 'UAE', name: 'United Arab Emirates', flag: 'https://flagcdn.com/w320/ae.png', region: 'middle-east' },
                { code: 'QAT', name: 'Qatar', flag: 'https://flagcdn.com/w320/qa.png', region: 'middle-east' },
                { code: 'SAU', name: 'Saudi Arabia', flag: 'https://flagcdn.com/w320/sa.png', region: 'middle-east' },
                { code: 'TUR', name: 'Turkey', flag: 'https://flagcdn.com/w320/tr.png', region: 'middle-east' },

                // South America
                { code: 'BRA', name: 'Brazil', flag: 'https://flagcdn.com/w320/br.png', region: 'south-america' },
                { code: 'ARG', name: 'Argentina', flag: 'https://flagcdn.com/w320/ar.png', region: 'south-america' },
                { code: 'CHL', name: 'Chile', flag: 'https://flagcdn.com/w320/cl.png', region: 'south-america' },

                // Africa
                { code: 'ZAF', name: 'South Africa', flag: 'https://flagcdn.com/w320/za.png', region: 'africa' },
                { code: 'EGY', name: 'Egypt', flag: 'https://flagcdn.com/w320/eg.png', region: 'africa' },
                { code: 'MAR', name: 'Morocco', flag: 'https://flagcdn.com/w320/ma.png', region: 'africa' }
            ];

            const track = document.getElementById('countriesTrack');
            if (!track) return;

            // Create countries twice for seamless loop
            const allCountries = [...countries, ...countries];

            allCountries.forEach((country, index) => {
                const countryItem = document.createElement('div');
                countryItem.className = 'country-item';

                countryItem.innerHTML = `
                    <img src="${country.flag}" alt="${country.name} Flag" class="country-flag-single">
                    <div class="country-info">
                        <div class="country-code">${country.code}</div>
                        <div class="country-full-name">${country.name}</div>
                    </div>
                `;

                // Add click handler for navigation
                countryItem.addEventListener('click', () => {
                    window.location.href = `search_universities.html?country=${country.code.toLowerCase()}`;
                });

                track.appendChild(countryItem);
            });

            // Calculate total width for animation
            const itemWidth = 180 + 25; // min-width + gap
            const totalWidth = allCountries.length * itemWidth;
            track.style.width = totalWidth + 'px';
        }

        initializeCountryRotator();

        // Scholarship data
        const scholarships = [
            {
                name: "Chevening Scholarships",
                country: "uk",
                type: "government",
                amount: "Full Funding",
                deadline: "November 2, 2025",
                eligibility: ["Bachelor's degree", "2+ years work experience", "IELTS 6.5+", "Leadership potential"],
                description: "UK government's global scholarship programme for outstanding emerging leaders.",
                link: "https://www.chevening.org/",
                funding: "Tuition fees, living allowance, travel costs"
            },
            {
                name: "Fulbright Foreign Student Program",
                country: "usa",
                type: "government",
                amount: "Full Funding",
                deadline: "October 15, 2025",
                eligibility: ["Bachelor's degree", "Strong academic record", "TOEFL 80+/IELTS 6.5+", "Leadership experience"],
                description: "Provides funding for graduate study, research, and teaching in the United States.",
                link: "https://foreign.fulbrightonline.org/",
                funding: "Tuition, living stipend, health insurance, travel"
            },
            {
                name: "DAAD Scholarships",
                country: "germany",
                type: "government",
                amount: "Partial Funding",
                deadline: "October 31, 2025",
                eligibility: ["Bachelor's degree", "German language skills (some programs)", "Academic excellence"],
                description: "German Academic Exchange Service scholarships for international students.",
                link: "https://www.daad.de/",
                funding: "€850-1,200/month + health insurance"
            },
            {
                name: "Vanier Canada Graduate Scholarships",
                country: "canada",
                type: "government",
                amount: "Full Funding",
                deadline: "November 1, 2025",
                eligibility: ["PhD program", "Canadian citizen/PR or international", "Academic excellence", "Research potential"],
                description: "Prestigious scholarship for doctoral students demonstrating leadership skills.",
                link: "https://vanier.gc.ca/",
                funding: "CAD $50,000/year for 3 years"
            },
            {
                name: "Australia Awards Scholarships",
                country: "australia",
                type: "government",
                amount: "Full Funding",
                deadline: "April 30, 2025",
                eligibility: ["Developing country citizen", "Bachelor's degree", "IELTS 6.5+", "Work experience"],
                description: "Australian government scholarships for students from developing countries.",
                link: "https://www.australiaawards.gov.au/",
                funding: "Tuition, living allowance, health cover, travel"
            },
            {
                name: "Holland Scholarship",
                country: "netherlands",
                type: "government",
                amount: "Partial Funding",
                deadline: "February 1, 2025",
                eligibility: ["Non-EU/EEA nationality", "Bachelor's/Master's program", "First-time in Netherlands"],
                description: "Scholarship for international students studying in the Netherlands.",
                link: "https://www.studyinholland.nl/",
                funding: "€5,000 for first year"
            },
            {
                name: "Swedish Institute Scholarships",
                country: "sweden",
                type: "government",
                amount: "Full Funding",
                deadline: "February 10, 2025",
                eligibility: ["Developing country citizen", "Master's program", "IELTS 6.5+", "Leadership potential"],
                description: "Scholarships for global professionals to pursue Master's studies in Sweden.",
                link: "https://si.se/",
                funding: "Tuition, living allowance, travel grant, insurance"
            },
            {
                name: "Eiffel Excellence Scholarship",
                country: "france",
                type: "government",
                amount: "Partial Funding",
                deadline: "January 8, 2025",
                eligibility: ["Master's/PhD program", "Non-French nationality", "Age limit: 30 (Master's), 35 (PhD)"],
                description: "French government scholarship for international students in priority fields.",
                link: "https://www.campusfrance.org/",
                funding: "€1,181/month (Master's), €1,700/month (PhD)"
            },
            {
                name: "Rhodes Scholarships",
                country: "uk",
                type: "merit",
                amount: "Full Funding",
                deadline: "October 1, 2025",
                eligibility: ["Bachelor's degree", "Age 18-24", "Academic excellence", "Leadership", "Service to others"],
                description: "World's oldest international scholarship programme at University of Oxford.",
                link: "https://www.rhodeshouse.ox.ac.uk/",
                funding: "University fees, living stipend, travel costs"
            },
            {
                name: "Gates Cambridge Scholarships",
                country: "uk",
                type: "merit",
                amount: "Full Funding",
                deadline: "December 3, 2025",
                eligibility: ["Non-UK citizen", "Postgraduate program", "Academic excellence", "Leadership potential"],
                description: "Prestigious scholarship for outstanding students at University of Cambridge.",
                link: "https://www.gatescambridge.org/",
                funding: "University composition fee, maintenance allowance, travel"
            },
            {
                name: "Erasmus Mundus Joint Master Degrees",
                country: "europe",
                type: "university",
                amount: "Full Funding",
                deadline: "January 15, 2025",
                eligibility: ["Bachelor's degree", "English proficiency", "Varies by program"],
                description: "EU-funded scholarships for joint Master's programmes across European universities.",
                link: "https://ec.europa.eu/programmes/erasmus-plus/",
                funding: "€1,400/month + travel and installation costs"
            },
            {
                name: "University of Melbourne Graduate Scholarships",
                country: "australia",
                type: "university",
                amount: "Partial Funding",
                deadline: "October 31, 2025",
                eligibility: ["International student", "Research degree", "Academic merit"],
                description: "Merit-based scholarships for international research students.",
                link: "https://scholarships.unimelb.edu.au/",
                funding: "Up to AUD $31,000/year"
            }
        ];

        // Function to render scholarships
        function renderScholarships(scholarshipsToShow = scholarships) {
            const container = document.getElementById('scholarshipsList');
            container.innerHTML = '';

            if (scholarshipsToShow.length === 0) {
                container.innerHTML = '<div class="col-12"><div class="alert alert-info">No scholarships found matching your criteria.</div></div>';
                return;
            }

            scholarshipsToShow.forEach((scholarship, index) => {
                const card = document.createElement('div');
                card.className = 'col-md-6 col-lg-6 mb-4';
                card.setAttribute('data-aos', 'fade-up');
                card.setAttribute('data-aos-delay', (index % 3) * 100);

                const countryFlags = {
                    'uk': '🇬🇧',
                    'usa': '🇺🇸',
                    'canada': '🇨🇦',
                    'germany': '🇩🇪',
                    'australia': '🇦🇺',
                    'netherlands': '🇳🇱',
                    'sweden': '🇸🇪',
                    'france': '🇫🇷',
                    'europe': '🇪🇺'
                };

                card.innerHTML = `
                    <div class="scholarship-card">
                        <div class="scholarship-header">
                            <h3 class="scholarship-title">${scholarship.name}</h3>
                            <span class="scholarship-amount">${scholarship.amount}</span>
                        </div>
                        <div class="scholarship-details">
                            <div class="scholarship-detail">
                                <i class="fas fa-globe text-primary"></i>
                                <span>${countryFlags[scholarship.country] || '🌍'} ${scholarship.country.toUpperCase()}</span>
                            </div>
                            <div class="scholarship-detail">
                                <i class="fas fa-graduation-cap text-primary"></i>
                                <span>${scholarship.type.charAt(0).toUpperCase() + scholarship.type.slice(1)} Scholarship</span>
                            </div>
                            <div class="scholarship-detail">
                                <i class="fas fa-money-bill-wave text-primary"></i>
                                <span>${scholarship.funding}</span>
                            </div>
                        </div>
                        <p class="scholarship-description">${scholarship.description}</p>
                        <div class="mb-3">
                            <strong>Eligibility:</strong><br>
                            ${scholarship.eligibility.map(req => `<span class="badge-custom">${req}</span>`).join('')}
                        </div>
                        <div class="scholarship-footer">
                            <span class="scholarship-deadline"><i class="fas fa-clock me-1"></i> Deadline: ${scholarship.deadline}</span>
                            <a href="${scholarship.link}" target="_blank" class="btn btn-primary">Apply Now</a>
                        </div>
                    </div>
                `;

                container.appendChild(card);
            });
        }

        // Filter functionality
        function filterScholarships() {
            const countryFilter = document.getElementById('countryFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const amountFilter = document.getElementById('amountFilter').value;

            let filtered = scholarships.filter(scholarship => {
                const matchesCountry = !countryFilter || scholarship.country === countryFilter;
                const matchesType = !typeFilter || scholarship.type === typeFilter;
                const matchesAmount = !amountFilter ||
                    (amountFilter === 'full' && scholarship.amount.includes('Full')) ||
                    (amountFilter === 'partial' && scholarship.amount.includes('Partial')) ||
                    (amountFilter === 'living' && scholarship.funding.toLowerCase().includes('living'));

                return matchesCountry && matchesType && matchesAmount;
            });

            renderScholarships(filtered);
        }

        // Dynamic Banner System
        const bannerConfigs = {
            'search_universities': {
                title: 'Go Abroad',
                description: 'Explore universities by region and find your perfect study destination.',
                background: 'https://images.unsplash.com/photo-1607237138185-eedd9c632b0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080&q=80',
                flags: ['🇬🇧', '🇺🇸', '🇨🇦', '🇦🇺', '🇩🇪']
            },
            'scholarships': {
                title: 'Go Abroad',
                description: 'Discover scholarship opportunities to fund your international education journey.',
                background: 'https://images.unsplash.com/photo-*************-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080&q=80',
                flags: ['💰', '🎓', '📚', '🏆', '💡']
            },
            'applications': {
                title: 'Go Abroad',
                description: 'Navigate your application process with our comprehensive guidance and tools.',
                background: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080&q=80',
                flags: ['📝', '📋', '✅', '📊', '🎯']
            },
            'admission_guide': {
                title: 'Go Abroad',
                description: 'Get expert guidance on admission requirements and application strategies.',
                background: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080&q=80',
                flags: ['📖', '🎓', '📚', '💼', '🌟']
            }
        };

        function updateBanner(pageType = 'scholarships') {
            const config = bannerConfigs[pageType];
            if (!config) return;

            const pageHeaderBg = document.querySelector('.page-header-bg');
            const pageTitle = document.querySelector('.page-title');
            const pageDescription = document.querySelector('.page-description');
            const hangingPhotos = document.querySelectorAll('.hanging-photo');

            // Update background with smooth transition
            if (pageHeaderBg) {
                pageHeaderBg.style.opacity = '0';
                setTimeout(() => {
                    pageHeaderBg.style.backgroundImage = `url('${config.background}')`;
                    pageHeaderBg.style.opacity = '1';
                }, 300);
            }

            // Update title and description with fade effect
            if (pageTitle) {
                pageTitle.style.opacity = '0';
                setTimeout(() => {
                    pageTitle.textContent = config.title;
                    pageTitle.style.opacity = '1';
                }, 150);
            }

            if (pageDescription) {
                pageDescription.style.opacity = '0';
                setTimeout(() => {
                    pageDescription.textContent = config.description;
                    pageDescription.style.opacity = '1';
                }, 200);
            }

            // Update hanging photos
            hangingPhotos.forEach((photo, index) => {
                if (config.flags[index]) {
                    photo.style.opacity = '0';
                    setTimeout(() => {
                        photo.textContent = config.flags[index];
                        photo.style.opacity = '1';
                    }, 100 + (index * 50));
                }
            });
        }

        // Handle navigation active states
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize banner
            const currentPage = window.location.pathname.split('/').pop().replace('.html', '') || 'scholarships';
            updateBanner(currentPage);

            // Add smooth transitions to banner elements
            const pageHeaderBg = document.querySelector('.page-header-bg');
            const pageTitle = document.querySelector('.page-title');
            const pageDescription = document.querySelector('.page-description');
            const hangingPhotos = document.querySelectorAll('.hanging-photo');

            if (pageHeaderBg) {
                pageHeaderBg.style.transition = 'opacity 0.3s ease, background-image 0.3s ease';
            }

            if (pageTitle) {
                pageTitle.style.transition = 'opacity 0.3s ease';
            }

            if (pageDescription) {
                pageDescription.style.transition = 'opacity 0.3s ease';
            }

            hangingPhotos.forEach(photo => {
                photo.style.transition = 'opacity 0.3s ease';
            });

            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

            // Set active link based on current page
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if ((currentPage === 'index.html' && (href === '#' || href === 'index.html')) ||
                    (href === currentPage)) {
                    link.classList.add('active');
                }
            });

            // Event listeners for filters
            document.getElementById('countryFilter').addEventListener('change', filterScholarships);
            document.getElementById('typeFilter').addEventListener('change', filterScholarships);
            document.getElementById('amountFilter').addEventListener('change', filterScholarships);

            // Mobile menu toggle
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            if (navbarToggler) {
                navbarToggler.addEventListener('click', function() {
                    navbarCollapse.classList.toggle('show');
                });
            }

            // Initial render
            renderScholarships();
        });
    </script>
</body>
</html>
