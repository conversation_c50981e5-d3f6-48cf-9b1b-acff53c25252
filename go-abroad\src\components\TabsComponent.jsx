import React, { useState, useEffect } from 'react';
import UniversityList from './UniversityList';
import AdmissionGuide from './AdmissionGuide';
import universities from '../data/universities.json';
import admissions from '../data/admissions.json';
import { FaGlobeEurope, FaUniversity, FaGraduationCap, FaFileAlt, FaBook } from 'react-icons/fa';

const regions = [
  { id: "UK", name: "UK", icon: "🇬🇧" },
  { id: "USA", name: "USA", icon: "🇺🇸" },
  { id: "Europe", name: "Europe", icon: "🇪🇺" },
  { id: "Canada", name: "Canada", icon: "🇨🇦" },
  { id: "Australia & New Zealand", name: "Australia & NZ", icon: "🇦🇺 🇳🇿" }
];

const TabsComponent = () => {
  const [activeTab, setActiveTab] = useState("UK");
  const [showWhyStudyAbroad, setShowWhyStudyAbroad] = useState(true);

  // Scroll to top of content when tab changes
  useEffect(() => {
    const contentElement = document.getElementById('region-content');
    if (contentElement) {
      contentElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [activeTab]);

  return (
    <div className="tabs-container">
      <h1 className="page-title">Search Universities</h1>
      <p className="page-description">
        Explore universities by region and find your perfect study destination.
      </p>
      
      <div className="region-tabs">
        {regions.map(region => (
          <button
            key={region.id}
            className={`region-tab ${activeTab === region.id ? 'active' : ''}`}
            onClick={() => setActiveTab(region.id)}
          >
            <span className="region-icon">{region.icon}</span> {region.name}
          </button>
        ))}
      </div>

      <div id="region-content" className="region-content">
        <UniversityList data={universities[activeTab]} region={activeTab} />
      </div>

      {/* Why Study Abroad Section */}
      {showWhyStudyAbroad && (
        <div className="why-study-abroad">
          <h2>Why Study Abroad?</h2>
          <p>Studying abroad offers numerous benefits for your academic and personal growth.</p>
          
          <div className="benefits-grid">
            <div className="benefit-card">
              <FaGlobeEurope className="benefit-icon" />
              <h3>Global Perspective</h3>
              <p>Gain a broader worldview and cultural understanding by immersing yourself in a new environment.</p>
            </div>
            
            <div className="benefit-card">
              <FaUniversity className="benefit-icon" />
              <h3>Quality Education</h3>
              <p>Access world-class teaching, research facilities, and innovative learning methods.</p>
            </div>
            
            <div className="benefit-card">
              <FaGraduationCap className="benefit-icon" />
              <h3>Career Opportunities</h3>
              <p>Enhance your resume with international experience and access global job markets.</p>
            </div>
            
            <div className="benefit-card">
              <FaFileAlt className="benefit-icon" />
              <h3>Personal Growth</h3>
              <p>Develop independence, adaptability, and problem-solving skills in a new environment.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TabsComponent;
