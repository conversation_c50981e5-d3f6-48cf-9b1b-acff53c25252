/* Modern Styles for Go Abroad Application
   Created as part of the frontend redesign
   This file contains shared styles for all pages
*/

:root {
  /* Primary Colors */
  --primary: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #60a5fa;

  /* Secondary Colors */
  --secondary: #00c9a7;
  --secondary-dark: #059669;
  --secondary-light: #34d399;

  /* Accent Colors */
  --accent: #8b5cf6;
  --accent-dark: #7c3aed;
  --accent-light: #a78bfa;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --black: #000000;

  /* Semantic Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-heading: var(--font-sans);
  --font-body: var(--font-sans);

  /* Spacing */
  --spacing-xs: 0.25rem;  /* 4px */
  --spacing-sm: 0.5rem;   /* 8px */
  --spacing-md: 1rem;     /* 16px */
  --spacing-lg: 1.5rem;   /* 24px */
  --spacing-xl: 2rem;     /* 32px */
  --spacing-2xl: 3rem;    /* 48px */
  --spacing-3xl: 4rem;    /* 64px */

  /* Border Radius */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */
  --radius-2xl: 1.5rem;   /* 24px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Transitions */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
  --transition-ease: cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* Z-index */
  --z-negative: -1;
  --z-elevate: 1;
  --z-dropdown: 10;
  --z-sticky: 100;
  --z-fixed: 200;
  --z-modal: 300;
  --z-popover: 400;
  --z-tooltip: 500;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-body);
  color: var(--gray-800);
  background-color: var(--gray-50);
  line-height: 1.6;
  overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
  color: var(--gray-900);
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

h5 {
  font-size: 1.125rem;
}

h6 {
  font-size: 1rem;
}

p {
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--transition-normal) var(--transition-ease);
}

a:hover {
  color: var(--primary-dark);
}

img {
  max-width: 100%;
  height: auto;
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Navbar */
.navbar {
  background-color: var(--white);
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  padding: var(--spacing-md) 0;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-200);
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.navbar-brand i {
  font-size: 1.75rem;
}

.navbar-menu {
  display: flex;
  gap: var(--spacing-md);
  list-style: none;
}

.navbar-item a {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-full);
  font-weight: 500;
  transition: all var(--transition-normal) var(--transition-ease);
  position: relative;
  color: var(--gray-700);
}

.navbar-item a:hover {
  color: var(--primary);
  background-color: var(--gray-100);
}

.navbar-item a.active {
  color: var(--white);
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  box-shadow: var(--shadow-md);
}

.navbar-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--gray-700);
  cursor: pointer;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-full);
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal) var(--transition-ease);
  border: none;
  gap: var(--spacing-sm);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--secondary-dark), var(--secondary));
}

.btn-outline {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
}

.btn-outline:hover {
  background-color: var(--primary);
  color: var(--white);
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: transform var(--transition-normal) var(--transition-ease),
              box-shadow var(--transition-normal) var(--transition-ease);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.card-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-body {
  padding: var(--spacing-lg);
}

.card-title {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-sm);
}

.card-text {
  color: var(--gray-600);
  margin-bottom: var(--spacing-md);
}

/* Footer */
.footer {
  background-color: var(--gray-900);
  color: var(--gray-300);
  padding: var(--spacing-2xl) 0 var(--spacing-lg);
  margin-top: var(--spacing-3xl);
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.footer-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: var(--spacing-md);
}

.footer-heading {
  font-size: 1.125rem;
  color: var(--white);
  margin-bottom: var(--spacing-lg);
}

.footer-links {
  list-style: none;
}

.footer-link {
  margin-bottom: var(--spacing-sm);
}

.footer-link a {
  color: var(--gray-400);
  transition: color var(--transition-normal) var(--transition-ease);
}

.footer-link a:hover {
  color: var(--white);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--gray-800);
  color: var(--gray-500);
}

/* Destination Cards */
.destination-card {
  overflow: hidden;
  transition: all var(--transition-normal) var(--transition-ease);
  height: 100%;
}

.card-img-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s var(--transition-ease);
}

.destination-card:hover .card-img {
  transform: scale(1.1);
}

.img-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.4));
  z-index: 1;
}

.destination-flag {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 2rem;
  background: var(--white);
  padding: 5px 10px;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-md);
  z-index: 2;
}

.university-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.university-tag {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: var(--white);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.8rem;
  font-weight: 500;
}

/* Feature Cards */
.feature-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-xl);
  height: 100%;
  transition: all var(--transition-normal) var(--transition-ease);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--white);
  margin-bottom: var(--spacing-lg);
}

.feature-title {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-sm);
  color: var(--gray-900);
}

.feature-text {
  color: var(--gray-600);
}

/* Testimonial Cards */
.testimonials-section {
  background-color: var(--gray-100);
}

.testimonial-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  height: 100%;
  transition: all var(--transition-normal) var(--transition-ease);
  display: flex;
  flex-direction: column;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.testimonial-content {
  flex: 1;
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.quote-icon {
  position: absolute;
  top: -15px;
  right: 0;
  font-size: 2rem;
  color: var(--primary-light);
  opacity: 0.3;
}

.testimonial-text {
  font-style: italic;
  color: var(--gray-700);
  line-height: 1.6;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.author-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--primary-light);
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--gray-900);
}

.author-university {
  font-size: 0.9rem;
  color: var(--gray-600);
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: var(--white);
  padding: var(--spacing-2xl) 0;
}

.cta-container {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  color: var(--white);
}

.cta-text {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
}

.btn-cta {
  background-color: var(--white);
  color: var(--primary);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: 1.1rem;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal) var(--transition-ease);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.btn-cta:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
  color: var(--primary-dark);
}

/* Social Icons */
.social-icons {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: var(--gray-300);
  transition: all var(--transition-normal) var(--transition-ease);
}

.social-icon:hover {
  background-color: var(--primary);
  color: var(--white);
  transform: translateY(-3px);
}

/* Footer Contact */
.footer-contact {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  color: var(--gray-400);
}

/* Media Queries */
@media (max-width: 768px) {
  .navbar-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--white);
    flex-direction: column;
    padding: var(--spacing-md);
    box-shadow: var(--shadow-md);
  }

  .navbar-menu.active {
    display: flex;
  }

  .navbar-toggle {
    display: block;
  }

  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.75rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-buttons {
    flex-direction: column;
    width: 100%;
  }

  .btn-hero-primary, .btn-hero-secondary {
    width: 100%;
    justify-content: center;
  }

  .hero-image {
    margin-top: var(--spacing-xl);
  }

  .footer-grid {
    grid-template-columns: 1fr;
  }
}
