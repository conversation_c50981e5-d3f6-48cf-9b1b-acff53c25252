import React, { useState } from 'react';
import { FaPlus, FaFileUpload, FaCalendarAlt, FaCheckCircle, FaExclamationTriangle, FaHourglassHalf } from 'react-icons/fa';

const Applications = () => {
  const [applications, setApplications] = useState([
    {
      id: 1,
      university: "University of Toronto",
      program: "Master of Computer Science",
      country: "Canada",
      status: "In Progress",
      deadline: "2025-01-15",
      documents: [
        { name: "Transcript", uploaded: true },
        { name: "Statement of Purpose", uploaded: true },
        { name: "Recommendation Letters", uploaded: false },
        { name: "CV/Resume", uploaded: true },
        { name: "English Test Score", uploaded: false }
      ]
    },
    {
      id: 2,
      university: "Imperial College London",
      program: "MSc in Data Science",
      country: "UK",
      status: "Submitted",
      deadline: "2024-12-15",
      documents: [
        { name: "Transcript", uploaded: true },
        { name: "Statement of Purpose", uploaded: true },
        { name: "Recommendation Letters", uploaded: true },
        { name: "CV/Resume", uploaded: true },
        { name: "English Test Score", uploaded: true }
      ]
    }
  ]);

  const [showNewApplicationForm, setShowNewApplicationForm] = useState(false);
  const [newApplication, setNewApplication] = useState({
    university: "",
    program: "",
    country: "",
    deadline: "",
    documents: [
      { name: "Transcript", uploaded: false },
      { name: "Statement of Purpose", uploaded: false },
      { name: "Recommendation Letters", uploaded: false },
      { name: "CV/Resume", uploaded: false },
      { name: "English Test Score", uploaded: false }
    ]
  });

  const toggleNewApplicationForm = () => {
    setShowNewApplicationForm(!showNewApplicationForm);
  };

  const handleNewApplicationChange = (e) => {
    const { name, value } = e.target;
    setNewApplication({
      ...newApplication,
      [name]: value
    });
  };

  const addNewApplication = (e) => {
    e.preventDefault();
    const newId = applications.length > 0 ? Math.max(...applications.map(app => app.id)) + 1 : 1;
    
    setApplications([
      ...applications,
      {
        ...newApplication,
        id: newId,
        status: "Not Started"
      }
    ]);
    
    setNewApplication({
      university: "",
      program: "",
      country: "",
      deadline: "",
      documents: [
        { name: "Transcript", uploaded: false },
        { name: "Statement of Purpose", uploaded: false },
        { name: "Recommendation Letters", uploaded: false },
        { name: "CV/Resume", uploaded: false },
        { name: "English Test Score", uploaded: false }
      ]
    });
    
    setShowNewApplicationForm(false);
  };

  const toggleDocumentStatus = (appId, docIndex) => {
    setApplications(applications.map(app => {
      if (app.id === appId) {
        const updatedDocuments = [...app.documents];
        updatedDocuments[docIndex] = {
          ...updatedDocuments[docIndex],
          uploaded: !updatedDocuments[docIndex].uploaded
        };
        return {
          ...app,
          documents: updatedDocuments
        };
      }
      return app;
    }));
  };

  const getStatusIcon = (status) => {
    switch(status) {
      case "Submitted":
        return <FaCheckCircle className="status-icon submitted" />;
      case "In Progress":
        return <FaHourglassHalf className="status-icon in-progress" />;
      case "Not Started":
        return <FaExclamationTriangle className="status-icon not-started" />;
      default:
        return null;
    }
  };

  const calculateProgress = (documents) => {
    if (documents.length === 0) return 0;
    const uploadedCount = documents.filter(doc => doc.uploaded).length;
    return Math.round((uploadedCount / documents.length) * 100);
  };

  return (
    <div className="applications-container">
      <h1 className="page-title">My Applications</h1>
      <p className="page-description">
        Track and manage your university applications in one place.
      </p>

      <div className="applications-header">
        <button className="add-application-btn" onClick={toggleNewApplicationForm}>
          <FaPlus /> Add New Application
        </button>
      </div>

      {showNewApplicationForm && (
        <div className="new-application-form-container">
          <h3>Add New Application</h3>
          <form className="new-application-form" onSubmit={addNewApplication}>
            <div className="form-group">
              <label htmlFor="university">University Name</label>
              <input
                type="text"
                id="university"
                name="university"
                value={newApplication.university}
                onChange={handleNewApplicationChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="program">Program</label>
              <input
                type="text"
                id="program"
                name="program"
                value={newApplication.program}
                onChange={handleNewApplicationChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="country">Country</label>
              <input
                type="text"
                id="country"
                name="country"
                value={newApplication.country}
                onChange={handleNewApplicationChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="deadline">Application Deadline</label>
              <input
                type="date"
                id="deadline"
                name="deadline"
                value={newApplication.deadline}
                onChange={handleNewApplicationChange}
                required
              />
            </div>

            <div className="form-buttons">
              <button type="submit" className="submit-btn">Add Application</button>
              <button type="button" className="cancel-btn" onClick={toggleNewApplicationForm}>Cancel</button>
            </div>
          </form>
        </div>
      )}

      <div className="applications-grid">
        {applications.length > 0 ? (
          applications.map(application => (
            <div key={application.id} className="application-card">
              <div className="application-header">
                <h3>{application.university}</h3>
                <div className="application-status">
                  {getStatusIcon(application.status)}
                  <span>{application.status}</span>
                </div>
              </div>

              <div className="application-details">
                <p><strong>Program:</strong> {application.program}</p>
                <p><strong>Country:</strong> {application.country}</p>
                <p className="deadline">
                  <FaCalendarAlt className="deadline-icon" />
                  <strong>Deadline:</strong> {new Date(application.deadline).toLocaleDateString()}
                </p>
              </div>

              <div className="application-progress">
                <div className="progress-label">
                  <span>Document Progress</span>
                  <span>{calculateProgress(application.documents)}%</span>
                </div>
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ width: `${calculateProgress(application.documents)}%` }}
                  ></div>
                </div>
              </div>

              <div className="application-documents">
                <h4>Required Documents</h4>
                <ul className="documents-list">
                  {application.documents.map((doc, index) => (
                    <li key={index} className={`document-item ${doc.uploaded ? 'uploaded' : ''}`}>
                      <label className="document-label">
                        <input
                          type="checkbox"
                          checked={doc.uploaded}
                          onChange={() => toggleDocumentStatus(application.id, index)}
                        />
                        <span className="document-name">{doc.name}</span>
                        {doc.uploaded ? (
                          <span className="document-status uploaded">Uploaded</span>
                        ) : (
                          <span className="document-status pending">Pending</span>
                        )}
                      </label>
                      {!doc.uploaded && (
                        <button className="upload-btn">
                          <FaFileUpload /> Upload
                        </button>
                      )}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="application-actions">
                <button className="view-details-btn">View Details</button>
                {application.status !== "Submitted" && (
                  <button className="submit-application-btn">Submit Application</button>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="no-applications">
            <p>You haven't added any applications yet.</p>
            <button className="add-application-btn" onClick={toggleNewApplicationForm}>
              <FaPlus /> Add Your First Application
            </button>
          </div>
        )}
      </div>

      <div className="application-tips">
        <h2>Application Tips</h2>
        <div className="tips-grid">
          <div className="tip-card">
            <h3>Start Early</h3>
            <p>Begin your application process at least 6-12 months before the deadline.</p>
          </div>
          <div className="tip-card">
            <h3>Research Programs</h3>
            <p>Thoroughly research programs to ensure they align with your academic and career goals.</p>
          </div>
          <div className="tip-card">
            <h3>Prepare Documents</h3>
            <p>Gather all required documents well in advance and have them professionally translated if needed.</p>
          </div>
          <div className="tip-card">
            <h3>Proofread Everything</h3>
            <p>Have someone else review your application materials for errors and clarity.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Applications;
