import React from 'react';
import { FaMapMarkerAlt, FaPhone, FaEnvelope, FaFacebook, FaTwitter, FaInstagram, FaLinkedin } from 'react-icons/fa';

const Footer = () => {
  return (
    <footer className="footer">
      <div className="footer-content">
        <div className="footer-section">
          <h3>Go Abroad</h3>
          <p>Your gateway to international education opportunities.</p>
          <div className="social-icons">
            <a href="#" className="social-icon"><FaFacebook /></a>
            <a href="#" className="social-icon"><FaTwitter /></a>
            <a href="#" className="social-icon"><FaInstagram /></a>
            <a href="#" className="social-icon"><FaLinkedin /></a>
          </div>
        </div>

        <div className="footer-section">
          <h3>Contact Us</h3>
          <p><FaMapMarkerAlt className="footer-icon" /> Suite #07, 4th Floor, Zohra Heights, Main Market, Gulberg II, Lahore</p>
          <p><FaPhone className="footer-icon" /> 0333-4098338 / 0333-4098485</p>
          <p><FaEnvelope className="footer-icon" /> <EMAIL></p>
        </div>

        <div className="footer-section">
          <h3>Quick Links</h3>
          <ul className="footer-links">
            <li><a href="#">About Us</a></li>
            <li><a href="#">Services</a></li>
            <li><a href="#">FAQ</a></li>
            <li><a href="#">Privacy Policy</a></li>
            <li><a href="#">Terms of Service</a></li>
          </ul>
        </div>

        <div className="footer-section">
          <h3>Popular Tags</h3>
          <div className="footer-tags">
            <span className="footer-tag">No GRE/GMAT</span>
            <span className="footer-tag">Apply Without IELTS</span>
            <span className="footer-tag">Duolingo Accepted</span>
            <span className="footer-tag">PTE / TOEFL</span>
            <span className="footer-tag">Scholarships</span>
            <span className="footer-tag">Work Permit</span>
          </div>
        </div>
      </div>

      <div className="footer-bottom">
        <p>&copy; {new Date().getFullYear()} Go Abroad. All rights reserved.</p>
      </div>
    </footer>
  );
};

export default Footer;
