<!DOCTYPE html>
<html>
<head>
    <title>Go Abroad - Search Universities</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .header { background: #2196F3; color: white; padding: 15px; margin: -20px -20px 20px -20px; }
        .tabs { display: flex; gap: 0; background: white; border-radius: 8px; overflow: hidden; margin: 20px 0; }
        .tab { padding: 15px 25px; background: #f0f0f0; border: none; cursor: pointer; flex: 1; font-size: 14px; }
        .tab.active { background: #2196F3; color: white; font-weight: bold; }
        .content { background: white; padding: 20px; border-radius: 8px; min-height: 300px; }
        .uni { background: #f9f9f9; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #2196F3; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌍 Go Abroad - Search Universities</h1>
        <p>Find your perfect study destination worldwide</p>
    </div>

    <div class="tabs">
        <button class="tab active" id="tab1" onclick="showRegion('uk', this)">🇬🇧 UK</button>
        <button class="tab" id="tab2" onclick="showRegion('europe', this)">🇪🇺 Europe</button>
        <button class="tab" id="tab3" onclick="showRegion('usa', this)">🇺🇸 USA</button>
        <button class="tab" id="tab4" onclick="showRegion('canada', this)">🇨🇦 Canada</button>
        <button class="tab" id="tab5" onclick="showRegion('australia', this)">🇦🇺 Australia</button>
    </div>

    <div class="content" id="content">
        <h2>🇬🇧 United Kingdom</h2>
        <p>World-class education with globally recognized universities.</p>
        <div class="uni">
            <h3>University College London (UCL)</h3>
            <p><strong>QS Rank:</strong> 8 | <strong>Programs:</strong> Computer Science, Medicine, Law</p>
            <p><strong>Tuition:</strong> £23,000 - £35,000 | <strong>Deadline:</strong> January 15, 2025</p>
        </div>
        <div class="uni">
            <h3>Imperial College London</h3>
            <p><strong>QS Rank:</strong> 7 | <strong>Programs:</strong> Engineering, Medicine, Science</p>
            <p><strong>Tuition:</strong> £25,000 - £38,000 | <strong>Deadline:</strong> February 28, 2025</p>
        </div>
    </div>

    <script>
        function showRegion(region, clickedTab) {
            // Remove active class from all tabs
            var tabs = document.querySelectorAll('.tab');
            for (var i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }
            
            // Add active class to clicked tab
            clickedTab.classList.add('active');
            
            // Update content
            var content = document.getElementById('content');
            
            if (region === 'uk') {
                content.innerHTML = `
                    <h2>🇬🇧 United Kingdom</h2>
                    <p>World-class education with globally recognized universities.</p>
                    <div class="uni">
                        <h3>University College London (UCL)</h3>
                        <p><strong>QS Rank:</strong> 8 | <strong>Programs:</strong> Computer Science, Medicine, Law</p>
                        <p><strong>Tuition:</strong> £23,000 - £35,000 | <strong>Deadline:</strong> January 15, 2025</p>
                    </div>
                    <div class="uni">
                        <h3>Imperial College London</h3>
                        <p><strong>QS Rank:</strong> 7 | <strong>Programs:</strong> Engineering, Medicine, Science</p>
                        <p><strong>Tuition:</strong> £25,000 - £38,000 | <strong>Deadline:</strong> February 28, 2025</p>
                    </div>
                `;
            } else if (region === 'europe') {
                content.innerHTML = `
                    <h2>🇪🇺 Europe</h2>
                    <p>Diverse educational opportunities with affordable or free tuition.</p>
                    <div class="uni">
                        <h3>Technical University of Munich</h3>
                        <p><strong>QS Rank:</strong> 50 | <strong>Programs:</strong> Engineering, Computer Science</p>
                        <p><strong>Tuition:</strong> €0 (Public) | <strong>Deadline:</strong> May 31, 2025</p>
                    </div>
                    <div class="uni">
                        <h3>University of Amsterdam</h3>
                        <p><strong>QS Rank:</strong> 55 | <strong>Programs:</strong> Business, Psychology, Computer Science</p>
                        <p><strong>Tuition:</strong> €2,200 - €15,000 | <strong>Deadline:</strong> April 1, 2025</p>
                    </div>
                `;
            } else if (region === 'usa') {
                content.innerHTML = `
                    <h2>🇺🇸 United States</h2>
                    <p>Top universities with cutting-edge research opportunities.</p>
                    <div class="uni">
                        <h3>University of California, Los Angeles (UCLA)</h3>
                        <p><strong>QS Rank:</strong> 13 | <strong>Programs:</strong> Computer Science, Business, Film</p>
                        <p><strong>Tuition:</strong> $45,000 - $60,000 | <strong>Deadline:</strong> December 15, 2024</p>
                    </div>
                    <div class="uni">
                        <h3>University of Texas at Austin</h3>
                        <p><strong>QS Rank:</strong> 58 | <strong>Programs:</strong> Computer Science, Business, Engineering</p>
                        <p><strong>Tuition:</strong> $35,000 - $50,000 | <strong>Deadline:</strong> December 15, 2024</p>
                    </div>
                `;
            } else if (region === 'canada') {
                content.innerHTML = `
                    <h2>🇨🇦 Canada</h2>
                    <p>High-quality education with multicultural environment.</p>
                    <div class="uni">
                        <h3>University of Toronto</h3>
                        <p><strong>QS Rank:</strong> 21 | <strong>Programs:</strong> Computer Science, Medicine, Business</p>
                        <p><strong>Tuition:</strong> CAD $35,000 - $55,000 | <strong>Deadline:</strong> January 15, 2025</p>
                    </div>
                `;
            } else if (region === 'australia') {
                content.innerHTML = `
                    <h2>🇦🇺 Australia & New Zealand</h2>
                    <p>World-class education in beautiful locations.</p>
                    <div class="uni">
                        <h3>University of Melbourne</h3>
                        <p><strong>QS Rank:</strong> 14 | <strong>Programs:</strong> Medicine, Law, Engineering</p>
                        <p><strong>Tuition:</strong> AUD $35,000 - $50,000 | <strong>Deadline:</strong> October 31, 2024</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
