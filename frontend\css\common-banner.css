/*
 * Common Banner System for Go Abroad Application
 * Ensures consistent banner sizing across all pages
 */

/* Unified Banner System - Common Banner Class */
.common-banner, .page-header {
    position: relative;
    height: 220px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 140px; /* Account for sticky headers */
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s ease-out 0.3s forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Common Banner Background */
.page-header-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1;
}

/* Common Banner Overlay */
.page-header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

/* Common Banner Content */
.page-header-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    max-width: 700px;
    padding: 0 1.5rem;
}

/* Common Banner Title */
.page-title {
    font-size: 2.4rem;
    font-weight: 800;
    margin-bottom: 0.8rem;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.5px;
    color: white;
    line-height: 1.2;
    word-wrap: break-word;
    max-width: 100%;
}

/* Common Banner Description */
.page-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 1.2rem;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
    line-height: 1.5;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Common Paper Plane Animation */
.paper-plane {
    position: absolute;
    width: 30px;
    height: 30px;
    z-index: 3;
    animation: flyPlane 15s linear infinite;
}

.paper-plane svg {
    width: 100%;
    height: 100%;
    fill: rgba(255, 255, 255, 0.8);
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

@keyframes flyPlane {
    0% {
        left: -50px;
        top: 60%;
        transform: rotate(0deg);
    }
    25% {
        left: 25%;
        top: 40%;
        transform: rotate(-10deg);
    }
    50% {
        left: 50%;
        top: 30%;
        transform: rotate(5deg);
    }
    75% {
        left: 75%;
        top: 50%;
        transform: rotate(-5deg);
    }
    100% {
        left: calc(100% + 50px);
        top: 35%;
        transform: rotate(0deg);
    }
}

/* Common CTA Button */
.cta-button {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 8px 25px rgba(238, 90, 36, 0.3);
    transition: all 0.3s ease;
    margin-top: 20px;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(238, 90, 36, 0.4);
    color: white;
    text-decoration: none;
}

/* Unified Responsive Design */
@media (max-width: 768px) {
    .common-banner, .page-header {
        height: 200px;
        margin-top: 120px;
    }

    .page-title {
        font-size: 2.0rem;
        letter-spacing: -0.3px;
    }

    .page-description {
        font-size: 1.1rem;
    }

    .page-header-content {
        padding: 0 1rem;
    }
}

@media (max-width: 576px) {
    .common-banner, .page-header {
        height: 180px;
        margin-top: 100px;
    }

    .page-title {
        font-size: 1.7rem;
        letter-spacing: -0.2px;
        line-height: 1.1;
    }

    .page-description {
        font-size: 0.95rem;
        line-height: 1.4;
    }

    .page-header-content {
        padding: 0 0.75rem;
    }
}

@media (max-width: 400px) {
    .page-title {
        font-size: 1.5rem;
        letter-spacing: 0;
        line-height: 1.1;
    }

    .page-description {
        font-size: 0.9rem;
    }

    .page-header-content {
        padding: 0 0.5rem;
    }
}

/* Common Float Animation for Banner Elements */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(var(--rotation, 0deg)); }
    25% { transform: translateY(-8px) rotate(calc(var(--rotation, 0deg) + 2deg)); }
    50% { transform: translateY(-4px) rotate(calc(var(--rotation, 0deg) - 1deg)); }
    75% { transform: translateY(-12px) rotate(calc(var(--rotation, 0deg) + 1deg)); }
}
