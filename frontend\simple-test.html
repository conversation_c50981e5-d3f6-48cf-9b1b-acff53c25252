<!DOCTYPE html>
<html>
<head>
    <title>Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        
        .tab {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            cursor: pointer;
        }
        
        .tab.active {
            background-color: #2196F3;
            color: white;
        }
        
        .content {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <h1>Simple Tab Test</h1>
    
    <div class="tab active" onclick="showTab('uk')">UK</div>
    <div class="tab" onclick="showTab('europe')">Europe</div>
    <div class="tab" onclick="showTab('usa')">USA</div>
    
    <div class="content" id="content">
        <h2>UK Content</h2>
        <p>This is UK content.</p>
    </div>
    
    <script>
        function showTab(region) {
            alert('Tab clicked: ' + region);
            
            // Remove active class from all tabs
            var tabs = document.getElementsByClassName('tab');
            for (var i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }
            
            // Add active class to clicked tab
            event.target.classList.add('active');
            
            // Update content
            var content = document.getElementById('content');
            content.innerHTML = '<h2>' + region.toUpperCase() + ' Content</h2><p>This is ' + region + ' content.</p>';
        }
    </script>
</body>
</html>
