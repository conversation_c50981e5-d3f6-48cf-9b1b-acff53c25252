<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Universities - Go Abroad</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f2f5;
            color: #333333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: #ffffff;
            padding: 15px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo {
            width: 40px;
            height: 40px;
            background-color: #ffffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #2196F3;
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
            list-style: none;
        }

        .nav-item {
            color: #ffffff;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-item:hover, .nav-item.active {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .page-header {
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2.5rem;
            color: #2196F3;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .page-description {
            color: #666666;
            font-size: 1.1rem;
        }

        .highlight {
            color: #2196F3;
            font-weight: 600;
        }

        .region-tabs {
            display: flex;
            gap: 0;
            margin-bottom: 30px;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tab-btn {
            background-color: #f5f7fa;
            color: #333333;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            font-weight: 500;
            border-right: 1px solid #e0e0e0;
            flex: 1;
            text-align: center;
            outline: none;
            font-size: 14px;
            font-family: inherit;
            transition: all 0.3s ease;
        }

        .tab-btn:last-child {
            border-right: none;
        }

        .tab-btn:hover {
            background-color: #e3f2fd;
        }

        .tab-btn.active {
            background-color: #2196F3;
            color: #ffffff;
            font-weight: 600;
        }

        .content-area {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            min-height: 400px;
        }

        .region-title {
            font-size: 1.8rem;
            color: #2196F3;
            margin-bottom: 15px;
        }

        .region-description {
            color: #666666;
            margin-bottom: 20px;
        }

        .university-card {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }

        .university-name {
            color: #2196F3;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .university-details {
            color: #666666;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-container">
            <div class="logo-section">
                <div class="logo">✈️</div>
                <div class="logo-text">Go Abroad</div>
            </div>
            <nav class="nav-menu">
                <a href="#" class="nav-item">Home</a>
                <a href="#" class="nav-item active">Search Universities</a>
                <a href="#" class="nav-item">Scholarships</a>
                <a href="#" class="nav-item">Applications</a>
                <a href="#" class="nav-item">Admission Guide</a>
            </nav>
        </div>
    </header>

    <div class="main-container">
        <div class="page-header">
            <h1 class="page-title">Search Universities</h1>
            <p class="page-description">
                Explore universities by <span class="highlight">region</span> and find your perfect study destination.
            </p>
        </div>

        <div class="region-tabs">
            <button class="tab-btn active" onclick="switchTab(this, 'uk')">🇬🇧 UK</button>
            <button class="tab-btn" onclick="switchTab(this, 'europe')">🇪🇺 Europe</button>
            <button class="tab-btn" onclick="switchTab(this, 'usa')">🇺🇸 USA</button>
            <button class="tab-btn" onclick="switchTab(this, 'canada')">🇨🇦 Canada</button>
            <button class="tab-btn" onclick="switchTab(this, 'australia')">🇦🇺 Australia & NZ</button>
        </div>

        <div class="content-area" id="content">
            <h2 class="region-title">🇬🇧 United Kingdom</h2>
            <p class="region-description">The United Kingdom offers world-class education with globally recognized universities and diverse program options.</p>
            
            <div class="university-card">
                <div class="university-name">University College London</div>
                <div class="university-details">
                    🏆 QS Rank: 8<br>
                    📚 Programs: Computer Science, Medicine, Law<br>
                    💰 Tuition: £23,000 - £35,000<br>
                    📅 Deadline: January 15, 2025
                </div>
            </div>
            
            <div class="university-card">
                <div class="university-name">Imperial College London</div>
                <div class="university-details">
                    🏆 QS Rank: 7<br>
                    📚 Programs: Engineering, Medicine, Science<br>
                    💰 Tuition: £25,000 - £38,000<br>
                    📅 Deadline: February 28, 2025
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(clickedTab, region) {
            // Remove active class from all tabs
            document.querySelectorAll('.tab-btn').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Add active class to clicked tab
            clickedTab.classList.add('active');
            
            // Update content based on region
            const content = document.getElementById('content');
            
            if (region === 'uk') {
                content.innerHTML = `
                    <h2 class="region-title">🇬🇧 United Kingdom</h2>
                    <p class="region-description">The United Kingdom offers world-class education with globally recognized universities and diverse program options.</p>
                    
                    <div class="university-card">
                        <div class="university-name">University College London</div>
                        <div class="university-details">
                            🏆 QS Rank: 8<br>
                            📚 Programs: Computer Science, Medicine, Law<br>
                            💰 Tuition: £23,000 - £35,000<br>
                            📅 Deadline: January 15, 2025
                        </div>
                    </div>
                    
                    <div class="university-card">
                        <div class="university-name">Imperial College London</div>
                        <div class="university-details">
                            🏆 QS Rank: 7<br>
                            📚 Programs: Engineering, Medicine, Science<br>
                            💰 Tuition: £25,000 - £38,000<br>
                            📅 Deadline: February 28, 2025
                        </div>
                    </div>
                `;
            } else if (region === 'europe') {
                content.innerHTML = `
                    <h2 class="region-title">🇪🇺 Europe</h2>
                    <p class="region-description">Europe offers diverse educational opportunities with many programs taught in English and affordable or free tuition in several countries.</p>
                    
                    <div class="university-card">
                        <div class="university-name">Technical University of Munich</div>
                        <div class="university-details">
                            🏆 QS Rank: 50<br>
                            📚 Programs: Engineering, Computer Science<br>
                            💰 Tuition: €0 (Public)<br>
                            📅 Deadline: May 31, 2025
                        </div>
                    </div>
                `;
            } else if (region === 'usa') {
                content.innerHTML = `
                    <h2 class="region-title">🇺🇸 United States</h2>
                    <p class="region-description">The United States hosts some of the world's top universities with flexible education systems and cutting-edge research opportunities.</p>
                    
                    <div class="university-card">
                        <div class="university-name">University of California, Los Angeles</div>
                        <div class="university-details">
                            🏆 QS Rank: 13<br>
                            📚 Programs: Computer Science, Business, Film<br>
                            💰 Tuition: $45,000 - $60,000<br>
                            📅 Deadline: December 15, 2024
                        </div>
                    </div>
                `;
            } else {
                content.innerHTML = `
                    <h2 class="region-title">${region.charAt(0).toUpperCase() + region.slice(1)}</h2>
                    <p class="region-description">University information for this region is coming soon. Please check back later or contact our advisors for assistance.</p>
                    
                    <div style="text-align: center; padding: 50px;">
                        <h3>Coming Soon</h3>
                        <p>We're currently gathering comprehensive university information for this region.</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
