import React, { useState } from 'react';
import { FaStar, FaGraduationCap, FaMoneyBillWave, FaCalendarAlt, FaSearch } from 'react-icons/fa';
import UniversityModal from './UniversityModal';

const UniversityList = ({ data, region }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUniversity, setSelectedUniversity] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [expandedCountry, setExpandedCountry] = useState(null);

  if (!data) return <p>No data available for {region}.</p>;

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const toggleCountry = (country) => {
    if (expandedCountry === country) {
      setExpandedCountry(null);
    } else {
      setExpandedCountry(country);
    }
  };

  const openUniversityModal = (university) => {
    setSelectedUniversity(university);
    setShowModal(true);
  };

  const closeUniversityModal = () => {
    setShowModal(false);
  };

  // Filter universities based on search term
  const filteredData = {};
  Object.keys(data).forEach(country => {
    const filteredUniversities = data[country].filter(uni => 
      uni.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (uni.programs && uni.programs.some(program => 
        program.toLowerCase().includes(searchTerm.toLowerCase())
      ))
    );
    
    if (filteredUniversities.length > 0) {
      filteredData[country] = filteredUniversities;
    }
  });

  return (
    <div className="university-list">
      <div className="search-container">
        <div className="search-input-wrapper">
          <FaSearch className="search-icon" />
          <input
            type="text"
            placeholder="Search universities or programs..."
            value={searchTerm}
            onChange={handleSearch}
            className="search-input"
          />
        </div>
      </div>

      {Object.keys(filteredData).length === 0 ? (
        <div className="no-results">
          <p>No universities found matching "{searchTerm}"</p>
        </div>
      ) : (
        Object.keys(filteredData).map(country => (
          <div key={country} className="country-section">
            <div 
              className={`country-header ${expandedCountry === country ? 'expanded' : ''}`}
              onClick={() => toggleCountry(country)}
            >
              <h3>{country}</h3>
              <span className="toggle-icon">{expandedCountry === country ? '▼' : '▶'}</span>
            </div>
            
            {expandedCountry === country && (
              <div className="universities-grid">
                {filteredData[country].map((uni, idx) => (
                  <div className="university-card" key={idx}>
                    <div className="university-logo">
                      <img src={uni.logo || 'https://via.placeholder.com/80'} alt={uni.name} />
                    </div>
                    <h4 className="university-name">{uni.name}</h4>
                    
                    <div className="university-ranking">
                      <FaStar className="ranking-icon" />
                      <span>Ranking: {uni.ranking}</span>
                    </div>
                    
                    <div className="university-programs">
                      <FaGraduationCap className="programs-icon" />
                      <span>{uni.programs ? uni.programs.join(', ') : 'Various Programs'}</span>
                    </div>
                    
                    <div className="university-tuition">
                      <FaMoneyBillWave className="tuition-icon" />
                      <span>Tuition: {uni.tuition || 'Contact for details'}</span>
                    </div>
                    
                    <div className="university-deadline">
                      <FaCalendarAlt className="deadline-icon" />
                      <span>Deadline: {uni.deadline || 'Check university website'}</span>
                    </div>
                    
                    <div className="university-requirements">
                      {uni.requirements && Object.entries(uni.requirements).map(([test, score], i) => (
                        <span key={i} className="requirement-badge">{test}: {score}</span>
                      ))}
                    </div>
                    
                    <button 
                      className="view-details-btn"
                      onClick={() => openUniversityModal(uni)}
                    >
                      View Details
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))
      )}

      {showModal && selectedUniversity && (
        <UniversityModal 
          university={selectedUniversity} 
          onClose={closeUniversityModal} 
        />
      )}
    </div>
  );
};

export default UniversityList;
