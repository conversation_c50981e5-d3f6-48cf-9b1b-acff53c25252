import React, { useState } from 'react';
import {
  FaHome,
  FaSearch,
  FaGraduationCap,
  FaFileAlt,
  FaBook,
  FaBars,
  FaTimes
} from 'react-icons/fa';
import { MdLanguage } from 'react-icons/md';
import logo from '../assets/logo.svg';

const Sidebar = ({ activeItem, setActiveItem }) => {
  const [collapsed, setCollapsed] = useState(window.innerWidth < 768);

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const handleNavClick = (item) => {
    setActiveItem(item);
    if (window.innerWidth < 768) {
      setCollapsed(true);
    }
  };

  return (
    <>
      <div className={`sidebar ${collapsed ? 'collapsed' : ''}`}>
        <div className="sidebar-header">
          <img src={logo || 'https://via.placeholder.com/40'} alt="Go Abroad Logo" className="logo" />
          <h1 className="sidebar-title">Study Go Abroad</h1>
          <button className="sidebar-toggle" onClick={toggleSidebar}>
            {collapsed ? <FaBars /> : <FaTimes />}
          </button>
        </div>

        <div className="sidebar-nav">
          <div
            className={`sidebar-nav-item ${activeItem === 'home' ? 'active' : ''}`}
            onClick={() => handleNavClick('home')}
          >
            <FaHome className="sidebar-icon" />
            <span className="sidebar-text">Home</span>
          </div>

          <div
            className={`sidebar-nav-item ${activeItem === 'search' ? 'active' : ''}`}
            onClick={() => handleNavClick('search')}
          >
            <FaSearch className="sidebar-icon" />
            <span className="sidebar-text">Search Universities</span>
          </div>

          <div
            className={`sidebar-nav-item ${activeItem === 'scholarships' ? 'active' : ''}`}
            onClick={() => handleNavClick('scholarships')}
          >
            <FaGraduationCap className="sidebar-icon" />
            <span className="sidebar-text">Scholarships</span>
          </div>

          <div
            className={`sidebar-nav-item ${activeItem === 'applications' ? 'active' : ''}`}
            onClick={() => handleNavClick('applications')}
          >
            <FaFileAlt className="sidebar-icon" />
            <span className="sidebar-text">Applications</span>
          </div>

          <div
            className={`sidebar-nav-item ${activeItem === 'admission-guide' ? 'active' : ''}`}
            onClick={() => handleNavClick('admission-guide')}
          >
            <FaBook className="sidebar-icon" />
            <span className="sidebar-text">Admission Guide</span>
          </div>
        </div>

        <div className="sidebar-footer">
          <div className="language-selector">
            <MdLanguage className="language-icon" />
            <select className="language-select">
              <option value="en">English</option>
              <option value="es">Español</option>
              <option value="fr">Français</option>
              <option value="de">Deutsch</option>
              <option value="ar">العربية</option>
            </select>
          </div>
        </div>
      </div>

      {/* Mobile overlay */}
      {!collapsed && window.innerWidth < 768 && (
        <div className="sidebar-overlay" onClick={toggleSidebar}></div>
      )}

      {/* Mobile toggle button */}
      {collapsed && window.innerWidth < 768 && (
        <button className="mobile-toggle" onClick={toggleSidebar}>
          <FaBars />
        </button>
      )}
    </>
  );
};

export default Sidebar;
