<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Applications - Go Abroad</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/modern-styles.css">
    <link rel="stylesheet" href="css/common-banner.css">
    <style>
        /* Unified Banner System - Common Banner Class */
        .common-banner, .page-header {
            position: relative;
            height: 220px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 140px; /* Account for sticky headers */
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .page-header-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                linear-gradient(135deg, rgba(0, 102, 204, 0.85) 0%, rgba(0, 201, 167, 0.85) 100%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 400"><defs><pattern id="worldMap" patternUnits="userSpaceOnUse" width="1200" height="400"><g opacity="0.1" fill="white"><path d="M100,150 Q200,100 300,150 T500,140 Q600,120 700,140 T900,130 Q1000,110 1100,130"/><circle cx="200" cy="140" r="3"/><circle cx="400" cy="150" r="3"/><circle cx="600" cy="130" r="3"/><circle cx="800" cy="140" r="3"/><circle cx="1000" cy="120" r="3"/></g></pattern></defs><rect width="100%" height="100%" fill="url(%23worldMap)"/></svg>');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 1;
        }

        .page-header-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(0, 102, 204, 0.7) 0%,
                rgba(0, 201, 167, 0.7) 100%);
            z-index: 2;
        }

        .page-header-content {
            position: relative;
            z-index: 3;
            text-align: center;
            color: white;
            max-width: 700px;
            padding: 0 1.5rem;
        }

        .page-title {
            font-size: 2.8rem;
            font-weight: 800;
            margin-bottom: 0.8rem;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            letter-spacing: -1px;
            color: white;
        }

        .page-description {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.95);
            margin-bottom: 1.2rem;
            text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
            line-height: 1.5;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Paper Plane Animation */
        .paper-plane {
            position: absolute;
            width: 30px;
            height: 30px;
            z-index: 3;
            animation: flyPlane 15s linear infinite;
        }

        .paper-plane svg {
            width: 100%;
            height: 100%;
            fill: rgba(255, 255, 255, 0.8);
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        @keyframes flyPlane {
            0% {
                left: -50px;
                top: 60%;
                transform: rotate(0deg);
            }
            25% {
                left: 25%;
                top: 40%;
                transform: rotate(-10deg);
            }
            50% {
                left: 50%;
                top: 30%;
                transform: rotate(5deg);
            }
            75% {
                left: 75%;
                top: 50%;
                transform: rotate(-5deg);
            }
            100% {
                left: calc(100% + 50px);
                top: 35%;
                transform: rotate(0deg);
            }
        }

        /* Landmark Cards with Flags */
        .landmark-cards {
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            pointer-events: none;
            z-index: 4;
            display: flex;
            justify-content: space-around;
            align-items: flex-start;
            padding: 1rem;
        }

        .landmark-card {
            position: relative;
            width: 90px;
            height: 110px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
            overflow: hidden;
            transform: rotate(var(--rotation, 0deg));
            animation: float 6s ease-in-out infinite;
            animation-delay: var(--delay, 0s);
            padding: 8px;
        }

        .landmark-card .flag {
            position: absolute;
            top: -6px;
            right: -6px;
            font-size: 1.2rem;
            z-index: 2;
            background: white;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .landmark-card .image {
            width: 100%;
            height: 65px;
            background-size: cover;
            background-position: center;
            border-radius: 8px;
            margin-bottom: 6px;
        }

        .landmark-card .title {
            font-size: 9px;
            font-weight: 600;
            color: #333;
            text-align: center;
            line-height: 1.1;
            padding: 0 4px;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(var(--rotation, 0deg)); }
            25% { transform: translateY(-8px) rotate(calc(var(--rotation, 0deg) + 2deg)); }
            50% { transform: translateY(-4px) rotate(calc(var(--rotation, 0deg) - 1deg)); }
            75% { transform: translateY(-12px) rotate(calc(var(--rotation, 0deg) + 1deg)); }
        }

        /* CTA Button */
        .cta-button {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 8px 25px rgba(238, 90, 36, 0.3);
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(238, 90, 36, 0.4);
            color: white;
            text-decoration: none;
        }



        /* Unified Responsive Design */
        @media (max-width: 768px) {
            .common-banner, .page-header {
                height: 200px;
                margin-top: 120px;
            }

            .page-title {
                font-size: 2.5rem;
            }

            .page-description {
                font-size: 1.1rem;
            }

            .landmark-cards {
                top: 15px;
                left: 15px;
                right: 15px;
            }

            .landmark-card {
                width: 80px;
                height: 95px;
                padding: 6px;
            }

            .landmark-card .title {
                font-size: 9px;
            }
        }

        @media (max-width: 576px) {
            .common-banner, .page-header {
                height: 180px;
                margin-top: 100px;
            }

            .page-title {
                font-size: 2rem;
            }

            .page-description {
                font-size: 1rem;
            }

            .landmark-cards {
                top: 10px;
                left: 10px;
                right: 10px;
            }

            .landmark-card {
                width: 70px;
                height: 85px;
                padding: 5px;
            }

            .landmark-card .title {
                font-size: 8px;
                padding: 6px;
            }

            .landmark-card .flag {
                width: 20px;
                height: 20px;
                font-size: 12px;
            }
        }

        .timeline-card {
            background: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: transform 0.3s var(--transition-ease);
        }

        .timeline-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .timeline-header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: var(--white);
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .timeline-body {
            padding: 1.5rem;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem;
            background-color: var(--gray-50);
            border-radius: var(--radius-md);
            transition: all 0.3s var(--transition-ease);
        }

        .timeline-item:hover {
            background-color: var(--gray-100);
            transform: translateX(5px);
        }

        .timeline-month {
            background: linear-gradient(135deg, var(--accent), var(--accent-dark));
            color: var(--white);
            padding: 0.5rem 1rem;
            border-radius: var(--radius-full);
            font-weight: 600;
            min-width: 100px;
            text-align: center;
            margin-right: 1.25rem;
            box-shadow: var(--shadow-sm);
        }

        .step-card {
            background: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            margin-bottom: 1.5rem;
            transition: transform 0.3s var(--transition-ease), box-shadow 0.3s var(--transition-ease);
            overflow: hidden;
        }

        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .step-header {
            background: linear-gradient(135deg, var(--secondary), var(--accent));
            color: var(--white);
            padding: 1.25rem 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .step-number {
            background-color: rgba(255, 255, 255, 0.2);
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        }

        .step-body {
            padding: 1.5rem;
        }

        .step-body ul {
            padding-left: 1.25rem;
        }

        .step-body li {
            margin-bottom: 0.5rem;
        }

        .document-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.75rem 1rem;
            background-color: var(--gray-50);
            border-radius: var(--radius-md);
            transition: all 0.3s var(--transition-ease);
        }

        .document-item:hover {
            background-color: var(--gray-100);
            transform: translateX(5px);
        }

        .document-icon {
            margin-right: 1rem;
            font-size: 1.2rem;
            color: var(--primary);
        }

        .platform-badge {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: var(--white);
            padding: 0.4rem 1rem;
            border-radius: var(--radius-full);
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-block;
            margin: 0.5rem 0.5rem 0.5rem 0;
            box-shadow: var(--shadow-sm);
        }

        .country-flag {
            font-size: 1.5rem;
            margin-right: 1rem;
        }

        .alert-custom {
            border-left: 4px solid var(--primary);
            background-color: var(--primary-light);
            border-radius: var(--radius-md);
            padding: 1.25rem;
            margin-bottom: 1.5rem;
        }

        .section-header {
            margin-bottom: 2rem;
            text-align: center;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--gray-900);
            position: relative;
            display: inline-block;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            border-radius: 3px;
        }

        .section-subtitle {
            color: var(--gray-600);
            font-size: 1.1rem;
            max-width: 700px;
            margin: 0 auto;
        }

        @media (max-width: 768px) {
            .timeline-item {
                flex-direction: column;
                text-align: center;
            }

            .timeline-month {
                margin-bottom: 0.75rem;
                margin-right: 0;
            }

            .document-item {
                flex-direction: column;
                text-align: center;
            }

            .document-icon {
                margin-right: 0;
                margin-bottom: 0.5rem;
            }
        }

        /* Header Banner Styles - Sticky */
        .header-banner {
            background: linear-gradient(135deg, #0066CC 0%, #00c9a7 100%);
            padding: 10px 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            overflow: hidden;
            z-index: 1001;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .header-banner-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            margin: 0;
            padding: 0 20px;
        }

        .scholarship-banner {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px 20px;
            border-radius: 25px;
            color: #0066CC;
            font-weight: 700;
            font-size: 1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .scholarship-text {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .scholarship-text i {
            color: #FFD700;
            font-size: 1.2rem;
        }

        .scholarship-cta {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        /* Expanded Country Flags Section */
        .country-rotator {
            flex: 1;
            height: 70px;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 35px;
            overflow: hidden;
            margin-left: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        /* Horizontal Scrolling Countries */
        .countries-track {
            display: flex;
            height: 100%;
            animation: scrollCountries 60s linear infinite;
            gap: 25px;
            padding: 0 25px;
            align-items: center;
        }

        .country-item {
            display: flex;
            align-items: center;
            gap: 15px;
            min-width: 180px;
            height: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 10px 15px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
        }

        .country-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .country-flag-single {
            width: 60px;
            height: 45px;
            border-radius: 10px;
            object-fit: contain;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
            border: 3px solid rgba(255, 255, 255, 0.5);
            background: white;
            flex-shrink: 0;
        }

        .country-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
            flex: 1;
        }

        .country-code {
            color: white;
            font-weight: 900;
            font-size: 1.4rem;
            text-shadow: 0 2px 6px rgba(0, 0, 0, 0.7);
            line-height: 1;
            letter-spacing: 0.8px;
        }

        .country-full-name {
            color: rgba(255, 255, 255, 0.95);
            font-size: 1rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            line-height: 1;
        }

        /* Scrolling Animation */
        @keyframes scrollCountries {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        /* Pause animation on hover */
        .country-rotator:hover .countries-track {
            animation-play-state: paused;
        }

        /* Modern Logo Styles */
        .modern-logo {
            text-decoration: none !important;
            transition: all 0.3s ease;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            border-radius: 25px;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 201, 167, 0.1));
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .logo-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #007bff, #00c9a7);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .modern-logo:hover .logo-container {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
            border-color: rgba(0, 123, 255, 0.3);
        }

        .modern-logo:hover .logo-container::before {
            opacity: 0.1;
        }

        .globe-icon {
            position: relative;
            font-size: 1.8rem;
            color: #007bff;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 201, 167, 0.1));
        }

        .flight-path {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            border: 2px dashed rgba(0, 201, 167, 0.4);
            border-radius: 50%;
            opacity: 0;
            transition: all 0.3s ease;
            animation: rotatePath 8s linear infinite;
        }

        .modern-logo:hover .flight-path {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.2);
        }

        .modern-logo:hover .globe-icon {
            color: #00c9a7;
            transform: scale(1.1);
            animation: globePulse 2s ease-in-out infinite;
        }

        .logo-text {
            font-family: 'Inter', 'Poppins', 'Montserrat', sans-serif;
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #007bff, #00c9a7);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            letter-spacing: -0.5px;
        }

        .modern-logo:hover .logo-text {
            transform: translateX(3px);
            filter: drop-shadow(0 4px 8px rgba(0, 123, 255, 0.3));
        }

        @keyframes rotatePath {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        @keyframes globePulse {
            0%, 100% { transform: scale(1.1); }
            50% { transform: scale(1.2); }
        }
    </style>
</head>
<body>
    <!-- Header Banner -->
    <div class="header-banner">
        <div class="header-banner-content">
            <div class="scholarship-banner">
                <div class="scholarship-text">
                    <i class="fas fa-graduation-cap"></i>
                    <span>Scholarships Available – Apply Now!</span>
                </div>
                <a href="scholarships.html" class="scholarship-cta">Apply Now</a>
            </div>

            <!-- Horizontal Scrolling Country Flags -->
            <div class="country-rotator">
                <div class="countries-track" id="countriesTrack">
                    <!-- Countries will be dynamically inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation - Sticky Below Header -->
    <nav class="navbar navbar-expand-lg fixed-top bg-white" style="top: 90px; z-index: 1000; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px);">
        <div class="container">
            <a class="navbar-brand modern-logo" href="index.html">
                <div class="logo-container">
                    <div class="globe-icon">
                        <i class="fas fa-globe-americas"></i>
                        <div class="flight-path"></div>
                    </div>
                    <span class="logo-text">Go Abroad</span>
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="search_universities.html">Search Universities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="scholarships.html">Scholarships</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="applications.html">Applications</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admission_guide.html">Admission Guide</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <header class="page-header common-banner">
        <div class="page-header-bg"></div>
        <div class="page-header-overlay"></div>

        <!-- Paper Plane Animation -->
        <div class="paper-plane">
            <svg viewBox="0 0 24 24">
                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
            </svg>
        </div>

        <!-- Landmark Cards with Flags -->
        <div class="landmark-cards">
            <div class="landmark-card" style="--rotation: -8deg; --delay: 0s;">
                <div class="flag">🇺🇸</div>
                <div class="image" style="background-image: url('https://images.unsplash.com/photo-1485738422979-f5c462d49f74?w=400&h=300&fit=crop&crop=center');"></div>
                <div class="title">Statue of Liberty<br>New York, USA</div>
            </div>
            <div class="landmark-card" style="--rotation: 5deg; --delay: 1s;">
                <div class="flag">🇬🇧</div>
                <div class="image" style="background-image: url('https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=400&h=300&fit=crop&crop=center');"></div>
                <div class="title">Tower Bridge<br>London, UK</div>
            </div>
            <div class="landmark-card" style="--rotation: -3deg; --delay: 2s;">
                <div class="flag">🇦🇺</div>
                <div class="image" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&crop=center');"></div>
                <div class="title">Sydney Opera House<br>Sydney, Australia</div>
            </div>
            <div class="landmark-card" style="--rotation: 7deg; --delay: 3s;">
                <div class="flag">🇨🇦</div>
                <div class="image" style="background-image: url('https://images.unsplash.com/photo-1517935706615-2717063c2225?w=400&h=300&fit=crop&crop=center');"></div>
                <div class="title">CN Tower<br>Toronto, Canada</div>
            </div>
        </div>

        <div class="container">
            <div class="page-header-content" data-aos="fade-up">
                <h1 class="page-title">Go Abroad – Apply with Confidence</h1>
                <p class="page-description">Step-by-step help for university, visa, and scholarship applications.</p>
                <a href="#timeline-section" class="cta-button">Explore Now</a>
            </div>
        </div>
    </header>

    <main>
        <div class="container">

        <!-- Application Timeline Section -->
        <section id="timeline-section" class="mb-5" data-aos="fade-up">
            <div class="section-header">
                <h2 class="section-title">Application Timeline by Country</h2>
                <p class="section-subtitle">Plan your application journey with these country-specific timelines</p>
            </div>
            <div class="row">
                <div class="col-lg-6">
                    <div class="timeline-card">
                        <div class="timeline-header">
                            <span class="country-flag">🇬🇧</span> United Kingdom
                        </div>
                        <div class="timeline-body">
                            <div class="timeline-item">
                                <div class="timeline-month">Sep-Oct</div>
                                <div>Research universities and prepare documents</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-month">Oct-Jan</div>
                                <div>Submit UCAS applications (deadline: January 25)</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-month">Feb-May</div>
                                <div>Receive offers and make decisions</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-month">Jun-Aug</div>
                                <div>Visa application and preparation</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="timeline-card">
                        <div class="timeline-header">
                            <span class="country-flag">🇺🇸</span> United States
                        </div>
                        <div class="timeline-body">
                            <div class="timeline-item">
                                <div class="timeline-month">Aug-Sep</div>
                                <div>Take SAT/ACT, prepare application materials</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-month">Nov-Jan</div>
                                <div>Submit applications (Early: Nov 1, Regular: Jan 1)</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-month">Mar-Apr</div>
                                <div>Receive admission decisions</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-month">May-Aug</div>
                                <div>Accept offer, apply for F-1 visa</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6">
                    <div class="timeline-card">
                        <div class="timeline-header">
                            <span class="country-flag">🇨🇦</span> Canada
                        </div>
                        <div class="timeline-body">
                            <div class="timeline-item">
                                <div class="timeline-month">Sep-Nov</div>
                                <div>Research programs and prepare documents</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-month">Dec-Mar</div>
                                <div>Submit applications (varies by university)</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-month">Apr-Jun</div>
                                <div>Receive offers and accept</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-month">Jul-Aug</div>
                                <div>Apply for study permit</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="timeline-card">
                        <div class="timeline-header">
                            <span class="country-flag">🇩🇪</span> Germany
                        </div>
                        <div class="timeline-body">
                            <div class="timeline-item">
                                <div class="timeline-month">Oct-Dec</div>
                                <div>Research programs and prepare documents</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-month">Jan-Mar</div>
                                <div>Submit applications via uni-assist or directly</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-month">Apr-Jul</div>
                                <div>Receive admission decisions</div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-month">Aug-Sep</div>
                                <div>Visa application and enrollment</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Documents Required Section -->
        <section class="mb-5" data-aos="fade-up">
            <div class="section-header">
                <h2 class="section-title">Required Documents by Country</h2>
                <p class="section-subtitle">Prepare all necessary documents for a successful application</p>
            </div>

            <div class="accordion" id="documentsAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#uk-docs">
                            <span class="country-flag">🇬🇧</span> United Kingdom - Required Documents
                        </button>
                    </h2>
                    <div id="uk-docs" class="accordion-collapse collapse show" data-bs-parent="#documentsAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Academic Documents:</h6>
                                    <div class="document-item">
                                        <span class="document-icon">📜</span>
                                        Academic transcripts (certified)
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">🎓</span>
                                        Degree certificates
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">📊</span>
                                        English test scores (IELTS/TOEFL/PTE)
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Supporting Documents:</h6>
                                    <div class="document-item">
                                        <span class="document-icon">📝</span>
                                        Personal Statement
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">👥</span>
                                        2 Academic References
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">📄</span>
                                        CV/Resume
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">🆔</span>
                                        Passport copy
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#usa-docs">
                            <span class="country-flag">🇺🇸</span> United States - Required Documents
                        </button>
                    </h2>
                    <div id="usa-docs" class="accordion-collapse collapse" data-bs-parent="#documentsAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Academic Documents:</h6>
                                    <div class="document-item">
                                        <span class="document-icon">📜</span>
                                        Official transcripts
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">📊</span>
                                        SAT/ACT scores (undergraduate)
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">📈</span>
                                        GRE/GMAT scores (graduate)
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">🗣️</span>
                                        TOEFL/IELTS scores
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Supporting Documents:</h6>
                                    <div class="document-item">
                                        <span class="document-icon">✍️</span>
                                        Essays/Personal Statement
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">👨‍🏫</span>
                                        Letters of Recommendation (2-3)
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">📄</span>
                                        Resume/CV
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">💰</span>
                                        Financial documents
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#canada-docs">
                            <span class="country-flag">🇨🇦</span> Canada - Required Documents
                        </button>
                    </h2>
                    <div id="canada-docs" class="accordion-collapse collapse" data-bs-parent="#documentsAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Academic Documents:</h6>
                                    <div class="document-item">
                                        <span class="document-icon">📜</span>
                                        Official transcripts
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">🎓</span>
                                        Degree certificates
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">📊</span>
                                        IELTS/TOEFL/CELPIP scores
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Supporting Documents:</h6>
                                    <div class="document-item">
                                        <span class="document-icon">📝</span>
                                        Statement of Purpose
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">👥</span>
                                        Letters of Reference (2-3)
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">📄</span>
                                        Resume
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">🆔</span>
                                        Passport copy
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#germany-docs">
                            <span class="country-flag">🇩🇪</span> Germany - Required Documents
                        </button>
                    </h2>
                    <div id="germany-docs" class="accordion-collapse collapse" data-bs-parent="#documentsAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Academic Documents:</h6>
                                    <div class="document-item">
                                        <span class="document-icon">📜</span>
                                        Certified academic transcripts
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">🎓</span>
                                        University entrance qualification
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">🗣️</span>
                                        German/English language certificate
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Supporting Documents:</h6>
                                    <div class="document-item">
                                        <span class="document-icon">📝</span>
                                        Motivation letter
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">📄</span>
                                        CV (Europass format)
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">🆔</span>
                                        Passport copy
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">📸</span>
                                        Passport photos
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#australia-docs">
                            <span class="country-flag">🇦🇺</span> Australia - Required Documents
                        </button>
                    </h2>
                    <div id="australia-docs" class="accordion-collapse collapse" data-bs-parent="#documentsAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Academic Documents:</h6>
                                    <div class="document-item">
                                        <span class="document-icon">📜</span>
                                        Academic transcripts
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">🎓</span>
                                        Completion certificates
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">📊</span>
                                        IELTS/TOEFL/PTE scores
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6>Supporting Documents:</h6>
                                    <div class="document-item">
                                        <span class="document-icon">📝</span>
                                        Personal Statement
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">👥</span>
                                        Academic references
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">📄</span>
                                        CV/Resume
                                    </div>
                                    <div class="document-item">
                                        <span class="document-icon">🆔</span>
                                        Passport copy
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Step-by-Step Process -->
        <section class="mb-5" data-aos="fade-up">
            <div class="section-header">
                <h2 class="section-title">Step-by-Step Application Process</h2>
                <p class="section-subtitle">Follow these steps to navigate the application journey successfully</p>
            </div>

            <div class="row">
                <div class="col-lg-4">
                    <div class="step-card">
                        <div class="step-header">
                            <div class="step-number">1</div>
                            <div>Research & Choose Universities</div>
                        </div>
                        <div class="step-body">
                            <ul>
                                <li>Research programs and universities</li>
                                <li>Check admission requirements</li>
                                <li>Compare tuition fees and living costs</li>
                                <li>Shortlist 5-8 universities</li>
                                <li>Check application deadlines</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="step-card">
                        <div class="step-header">
                            <div class="step-number">2</div>
                            <div>Prepare Required Documents</div>
                        </div>
                        <div class="step-body">
                            <ul>
                                <li>Take English proficiency tests</li>
                                <li>Take standardized tests (if required)</li>
                                <li>Get academic transcripts certified</li>
                                <li>Write personal statement/essays</li>
                                <li>Request letters of recommendation</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="step-card">
                        <div class="step-header">
                            <div class="step-number">3</div>
                            <div>Submit Applications Online</div>
                        </div>
                        <div class="step-body">
                            <ul>
                                <li>Create accounts on application platforms</li>
                                <li>Fill out application forms carefully</li>
                                <li>Upload all required documents</li>
                                <li>Pay application fees</li>
                                <li>Submit before deadlines</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4">
                    <div class="step-card">
                        <div class="step-header">
                            <div class="step-number">4</div>
                            <div>Track Application Status</div>
                        </div>
                        <div class="step-body">
                            <ul>
                                <li>Monitor application portals regularly</li>
                                <li>Respond to university requests promptly</li>
                                <li>Attend interviews if required</li>
                                <li>Submit additional documents if asked</li>
                                <li>Keep records of all communications</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="step-card">
                        <div class="step-header">
                            <div class="step-number">5</div>
                            <div>Receive & Accept Offers</div>
                        </div>
                        <div class="step-body">
                            <ul>
                                <li>Review all admission offers</li>
                                <li>Compare scholarships and financial aid</li>
                                <li>Accept your preferred offer</li>
                                <li>Pay enrollment deposit</li>
                                <li>Decline other offers politely</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="step-card">
                        <div class="step-header">
                            <div class="step-number">6</div>
                            <div>Visa Application & Preparation</div>
                        </div>
                        <div class="step-body">
                            <ul>
                                <li>Apply for student visa</li>
                                <li>Arrange accommodation</li>
                                <li>Book flights</li>
                                <li>Get health insurance</li>
                                <li>Prepare for departure</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Application Platforms Section -->
        <section class="mb-5" data-aos="fade-up">
            <div class="section-header">
                <h2 class="section-title">Country-Specific Application Platforms</h2>
                <p class="section-subtitle">Learn about the different application systems used around the world</p>
            </div>

            <div class="alert-custom">
                <h5>📌 Important Note:</h5>
                <p>Different countries use different application systems. Make sure to use the correct platform for your target country to avoid delays or rejections.</p>
            </div>

            <div class="row">
                <div class="col-lg-6">
                    <div class="timeline-card">
                        <div class="timeline-header">
                            <span class="country-flag">🇬🇧</span> United Kingdom - UCAS
                        </div>
                        <div class="timeline-body">
                            <p><strong>Platform:</strong> Universities and Colleges Admissions Service</p>
                            <p><strong>Website:</strong> <a href="https://www.ucas.com" target="_blank">www.ucas.com</a></p>
                            <p><strong>Features:</strong></p>
                            <ul>
                                <li>Apply to up to 5 universities with one application</li>
                                <li>Track application status online</li>
                                <li>Receive all offers through one portal</li>
                                <li>Personal statement required (4,000 characters)</li>
                            </ul>
                            <span class="platform-badge">Undergraduate & Postgraduate</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="timeline-card">
                        <div class="timeline-header">
                            <span class="country-flag">🇺🇸</span> United States - Common App
                        </div>
                        <div class="timeline-body">
                            <p><strong>Platform:</strong> Common Application</p>
                            <p><strong>Website:</strong> <a href="https://www.commonapp.org" target="_blank">www.commonapp.org</a></p>
                            <p><strong>Features:</strong></p>
                            <ul>
                                <li>Apply to 900+ colleges with one application</li>
                                <li>Standardized application format</li>
                                <li>Multiple essay prompts to choose from</li>
                                <li>Recommendation letter management</li>
                            </ul>
                            <span class="platform-badge">Undergraduate</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6">
                    <div class="timeline-card">
                        <div class="timeline-header">
                            <span class="country-flag">🇩🇪</span> Germany - uni-assist
                        </div>
                        <div class="timeline-body">
                            <p><strong>Platform:</strong> uni-assist e.V.</p>
                            <p><strong>Website:</strong> <a href="https://www.uni-assist.de" target="_blank">www.uni-assist.de</a></p>
                            <p><strong>Features:</strong></p>
                            <ul>
                                <li>Document verification service</li>
                                <li>Application processing for 180+ universities</li>
                                <li>Preliminary review of qualifications</li>
                                <li>Multiple language support</li>
                            </ul>
                            <span class="platform-badge">International Students</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="timeline-card">
                        <div class="timeline-header">
                            <span class="country-flag">🇨🇦</span> Canada - Direct Applications
                        </div>
                        <div class="timeline-body">
                            <p><strong>Platform:</strong> Individual University Portals</p>
                            <p><strong>Process:</strong> Apply directly to each university</p>
                            <p><strong>Features:</strong></p>
                            <ul>
                                <li>Each university has its own application system</li>
                                <li>Some provinces have centralized systems</li>
                                <li>Ontario: OUAC (Ontario Universities' Application Centre)</li>
                                <li>Quebec: SRAM for CEGEPs</li>
                            </ul>
                            <span class="platform-badge">University-Specific</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6">
                    <div class="timeline-card">
                        <div class="timeline-header">
                            <span class="country-flag">🇦🇺</span> Australia - Direct Applications
                        </div>
                        <div class="timeline-body">
                            <p><strong>Platform:</strong> Individual University Portals</p>
                            <p><strong>Process:</strong> Apply directly to each university</p>
                            <p><strong>Features:</strong></p>
                            <ul>
                                <li>Each university manages its own applications</li>
                                <li>Some use third-party platforms like ApplyDirect</li>
                                <li>Group of Eight universities have similar processes</li>
                                <li>Online application tracking available</li>
                            </ul>
                            <span class="platform-badge">University-Specific</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="timeline-card">
                        <div class="timeline-header">
                            <span class="country-flag">🇳🇱</span> Netherlands - Studielink
                        </div>
                        <div class="timeline-body">
                            <p><strong>Platform:</strong> Studielink</p>
                            <p><strong>Website:</strong> <a href="https://www.studielink.nl" target="_blank">www.studielink.nl</a></p>
                            <p><strong>Features:</strong></p>
                            <ul>
                                <li>Central application system for Dutch universities</li>
                                <li>Apply for student finance</li>
                                <li>Track enrollment status</li>
                                <li>Available in Dutch and English</li>
                            </ul>
                            <span class="platform-badge">All Programs</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Application Tips Section -->
        <section class="mb-5" data-aos="fade-up">
            <div class="section-header">
                <h2 class="section-title">Application Success Tips</h2>
                <p class="section-subtitle">Expert advice to help you stand out from other applicants</p>
            </div>

            <div class="row">
                <div class="col-lg-6">
                    <div class="step-card">
                        <div class="step-header">
                            <div class="step-number">📝</div>
                            <div>Writing Strong Essays</div>
                        </div>
                        <div class="step-body">
                            <ul>
                                <li><strong>Start early:</strong> Give yourself 2-3 months to write and revise</li>
                                <li><strong>Be authentic:</strong> Share your genuine experiences and motivations</li>
                                <li><strong>Show, don't tell:</strong> Use specific examples and stories</li>
                                <li><strong>Address the prompt:</strong> Answer exactly what is being asked</li>
                                <li><strong>Get feedback:</strong> Have others review your essays</li>
                                <li><strong>Proofread carefully:</strong> Check for grammar and spelling errors</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="step-card">
                        <div class="step-header">
                            <div class="step-number">📋</div>
                            <div>Document Preparation</div>
                        </div>
                        <div class="step-body">
                            <ul>
                                <li><strong>Get certified copies:</strong> Official transcripts and certificates</li>
                                <li><strong>Translate documents:</strong> Use certified translation services</li>
                                <li><strong>Scan in high quality:</strong> Clear, readable PDF files</li>
                                <li><strong>Follow naming conventions:</strong> Use clear file names</li>
                                <li><strong>Keep originals safe:</strong> Store physical documents securely</li>
                                <li><strong>Check requirements:</strong> Each university may have different needs</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6">
                    <div class="step-card">
                        <div class="step-header">
                            <div class="step-number">⏰</div>
                            <div>Meeting Deadlines</div>
                        </div>
                        <div class="step-body">
                            <ul>
                                <li><strong>Create a calendar:</strong> Mark all important dates</li>
                                <li><strong>Submit early:</strong> Don't wait until the last minute</li>
                                <li><strong>Check time zones:</strong> Understand deadline time zones</li>
                                <li><strong>Confirm submission:</strong> Get confirmation emails</li>
                                <li><strong>Plan for delays:</strong> Account for technical issues</li>
                                <li><strong>Track everything:</strong> Keep records of all submissions</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="step-card">
                        <div class="step-header">
                            <div class="step-number">💰</div>
                            <div>Financial Planning</div>
                        </div>
                        <div class="step-body">
                            <ul>
                                <li><strong>Budget for fees:</strong> Application fees can add up quickly</li>
                                <li><strong>Research costs:</strong> Tuition, living expenses, insurance</li>
                                <li><strong>Apply for aid:</strong> Submit scholarship applications early</li>
                                <li><strong>Show proof of funds:</strong> Bank statements for visa applications</li>
                                <li><strong>Consider exchange rates:</strong> Factor in currency fluctuations</li>
                                <li><strong>Plan for emergencies:</strong> Have extra funds available</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="footer-brand">
                        <i class="fas fa-globe-americas"></i> Go Abroad
                    </div>
                    <p class="footer-text">Your trusted partner for international education. We help students achieve their dreams of studying abroad.</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Quick Links</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="index.html">Home</a></li>
                        <li class="footer-link"><a href="search_universities.html">Universities</a></li>
                        <li class="footer-link"><a href="scholarships.html">Scholarships</a></li>
                        <li class="footer-link"><a href="applications.html">Applications</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Destinations</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="#">United Kingdom</a></li>
                        <li class="footer-link"><a href="#">United States</a></li>
                        <li class="footer-link"><a href="#">Canada</a></li>
                        <li class="footer-link"><a href="#">Australia</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Contact</h3>
                    <ul class="footer-links">
                        <li class="footer-contact"><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li class="footer-contact"><i class="fas fa-phone me-2"></i> +****************</li>
                        <li class="footer-contact"><i class="fas fa-map-marker-alt me-2"></i> New York, NY</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Go Abroad. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Horizontal Scrolling Countries Functionality
        function initializeCountryRotator() {
            const countries = [
                // Europe
                { code: 'UK', name: 'United Kingdom', flag: 'https://flagcdn.com/w320/gb.png', region: 'europe' },
                { code: 'GER', name: 'Germany', flag: 'https://flagcdn.com/w320/de.png', region: 'europe' },
                { code: 'FRA', name: 'France', flag: 'https://flagcdn.com/w320/fr.png', region: 'europe' },
                { code: 'ITA', name: 'Italy', flag: 'https://flagcdn.com/w320/it.png', region: 'europe' },
                { code: 'ESP', name: 'Spain', flag: 'https://flagcdn.com/w320/es.png', region: 'europe' },
                { code: 'NLD', name: 'Netherlands', flag: 'https://flagcdn.com/w320/nl.png', region: 'europe' },
                { code: 'SWE', name: 'Sweden', flag: 'https://flagcdn.com/w320/se.png', region: 'europe' },
                { code: 'CHE', name: 'Switzerland', flag: 'https://flagcdn.com/w320/ch.png', region: 'europe' },
                { code: 'NOR', name: 'Norway', flag: 'https://flagcdn.com/w320/no.png', region: 'europe' },
                { code: 'DNK', name: 'Denmark', flag: 'https://flagcdn.com/w320/dk.png', region: 'europe' },
                { code: 'FIN', name: 'Finland', flag: 'https://flagcdn.com/w320/fi.png', region: 'europe' },
                { code: 'AUT', name: 'Austria', flag: 'https://flagcdn.com/w320/at.png', region: 'europe' },
                { code: 'BEL', name: 'Belgium', flag: 'https://flagcdn.com/w320/be.png', region: 'europe' },
                { code: 'IRL', name: 'Ireland', flag: 'https://flagcdn.com/w320/ie.png', region: 'europe' },
                { code: 'POL', name: 'Poland', flag: 'https://flagcdn.com/w320/pl.png', region: 'europe' },
                { code: 'PRT', name: 'Portugal', flag: 'https://flagcdn.com/w320/pt.png', region: 'europe' },
                { code: 'GRC', name: 'Greece', flag: 'https://flagcdn.com/w320/gr.png', region: 'europe' },
                { code: 'CZE', name: 'Czech Republic', flag: 'https://flagcdn.com/w320/cz.png', region: 'europe' },
                { code: 'HUN', name: 'Hungary', flag: 'https://flagcdn.com/w320/hu.png', region: 'europe' },

                // North America
                { code: 'USA', name: 'United States', flag: 'https://flagcdn.com/w320/us.png', region: 'usa' },
                { code: 'CAN', name: 'Canada', flag: 'https://flagcdn.com/w320/ca.png', region: 'canada' },

                // Oceania
                { code: 'AUS', name: 'Australia', flag: 'https://flagcdn.com/w320/au.png', region: 'australia' },
                { code: 'NZL', name: 'New Zealand', flag: 'https://flagcdn.com/w320/nz.png', region: 'australia' },

                // Asia
                { code: 'JPN', name: 'Japan', flag: 'https://flagcdn.com/w320/jp.png', region: 'asia' },
                { code: 'KOR', name: 'South Korea', flag: 'https://flagcdn.com/w320/kr.png', region: 'asia' },
                { code: 'SGP', name: 'Singapore', flag: 'https://flagcdn.com/w320/sg.png', region: 'asia' },
                { code: 'HKG', name: 'Hong Kong', flag: 'https://flagcdn.com/w320/hk.png', region: 'asia' },
                { code: 'CHN', name: 'China', flag: 'https://flagcdn.com/w320/cn.png', region: 'asia' },
                { code: 'MYS', name: 'Malaysia', flag: 'https://flagcdn.com/w320/my.png', region: 'asia' },
                { code: 'THA', name: 'Thailand', flag: 'https://flagcdn.com/w320/th.png', region: 'asia' },

                // Middle East
                { code: 'UAE', name: 'United Arab Emirates', flag: 'https://flagcdn.com/w320/ae.png', region: 'middle-east' },
                { code: 'QAT', name: 'Qatar', flag: 'https://flagcdn.com/w320/qa.png', region: 'middle-east' },
                { code: 'SAU', name: 'Saudi Arabia', flag: 'https://flagcdn.com/w320/sa.png', region: 'middle-east' },
                { code: 'TUR', name: 'Turkey', flag: 'https://flagcdn.com/w320/tr.png', region: 'middle-east' },

                // South America
                { code: 'BRA', name: 'Brazil', flag: 'https://flagcdn.com/w320/br.png', region: 'south-america' },
                { code: 'ARG', name: 'Argentina', flag: 'https://flagcdn.com/w320/ar.png', region: 'south-america' },
                { code: 'CHL', name: 'Chile', flag: 'https://flagcdn.com/w320/cl.png', region: 'south-america' },

                // Africa
                { code: 'ZAF', name: 'South Africa', flag: 'https://flagcdn.com/w320/za.png', region: 'africa' },
                { code: 'EGY', name: 'Egypt', flag: 'https://flagcdn.com/w320/eg.png', region: 'africa' },
                { code: 'MAR', name: 'Morocco', flag: 'https://flagcdn.com/w320/ma.png', region: 'africa' }
            ];

            const track = document.getElementById('countriesTrack');
            if (!track) return;

            // Create countries twice for seamless loop
            const allCountries = [...countries, ...countries];

            allCountries.forEach((country, index) => {
                const countryItem = document.createElement('div');
                countryItem.className = 'country-item';

                countryItem.innerHTML = `
                    <img src="${country.flag}" alt="${country.name} Flag" class="country-flag-single">
                    <div class="country-info">
                        <div class="country-code">${country.code}</div>
                        <div class="country-full-name">${country.name}</div>
                    </div>
                `;

                // Add click handler for navigation
                countryItem.addEventListener('click', () => {
                    window.location.href = `search_universities.html?country=${country.code.toLowerCase()}`;
                });

                track.appendChild(countryItem);
            });

            // Calculate total width for animation
            const itemWidth = 180 + 25; // min-width + gap
            const totalWidth = allCountries.length * itemWidth;
            track.style.width = totalWidth + 'px';
        }

        initializeCountryRotator();

        // Handle navigation active states
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

            // Set active link based on current page
            const currentPage = window.location.pathname.split('/').pop() || 'index.html';
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if ((currentPage === 'index.html' && (href === '#' || href === 'index.html')) ||
                    (href === currentPage)) {
                    link.classList.add('active');
                }
            });

            // Mobile menu toggle
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            if (navbarToggler) {
                navbarToggler.addEventListener('click', function() {
                    navbarCollapse.classList.toggle('show');
                });
            }
        });
    </script>
</body>
</html>