import React from 'react';
import { FaSearch, FaGraduationCap, FaFileAlt, FaBook, FaArrowRight } from 'react-icons/fa';

const Home = ({ setActiveItem }) => {
  const navigateTo = (item) => {
    setActiveItem(item);
  };

  return (
    <div className="home-container">
      <div className="hero-section">
        <div className="hero-content">
          <h1>Your Gateway to Global Education</h1>
          <p>
            Discover world-class universities, scholarships, and admission requirements 
            to start your international education journey.
          </p>
          <button 
            className="cta-button"
            onClick={() => navigateTo('search')}
          >
            Explore Universities <FaArrowRight />
          </button>
        </div>
        <div className="hero-image">
          <img 
            src="https://img.freepik.com/free-vector/students-watching-webinar-computer-studying-online_74855-15522.jpg" 
            alt="Study Abroad" 
          />
        </div>
      </div>

      <div className="features-section">
        <h2>How We Can Help You</h2>
        <div className="features-grid">
          <div className="feature-card" onClick={() => navigateTo('search')}>
            <FaSearch className="feature-icon" />
            <h3>Find Universities</h3>
            <p>
              Browse through top universities worldwide, filter by country, 
              program, and requirements to find your perfect match.
            </p>
            <button className="feature-btn">Explore <FaArrowRight /></button>
          </div>

          <div className="feature-card" onClick={() => navigateTo('scholarships')}>
            <FaGraduationCap className="feature-icon" />
            <h3>Scholarships</h3>
            <p>
              Discover scholarships and financial aid opportunities 
              to fund your international education.
            </p>
            <button className="feature-btn">Find Scholarships <FaArrowRight /></button>
          </div>

          <div className="feature-card" onClick={() => navigateTo('applications')}>
            <FaFileAlt className="feature-icon" />
            <h3>Application Assistance</h3>
            <p>
              Get guidance on application processes, document requirements, 
              and deadlines for your target universities.
            </p>
            <button className="feature-btn">Learn More <FaArrowRight /></button>
          </div>

          <div className="feature-card" onClick={() => navigateTo('admission-guide')}>
            <FaBook className="feature-icon" />
            <h3>Admission Guide</h3>
            <p>
              Access comprehensive guides on admission requirements, 
              language tests, and visa procedures.
            </p>
            <button className="feature-btn">View Guide <FaArrowRight /></button>
          </div>
        </div>
      </div>

      <div className="stats-section">
        <div className="stat-card">
          <h3>500+</h3>
          <p>Universities</p>
        </div>
        <div className="stat-card">
          <h3>50+</h3>
          <p>Countries</p>
        </div>
        <div className="stat-card">
          <h3>10,000+</h3>
          <p>Students Helped</p>
        </div>
        <div className="stat-card">
          <h3>1,000+</h3>
          <p>Scholarships</p>
        </div>
      </div>

      <div className="testimonials-section">
        <h2>What Our Students Say</h2>
        <div className="testimonials-grid">
          <div className="testimonial-card">
            <div className="testimonial-content">
              <p>
                "Go Abroad helped me find the perfect university in the UK. 
                Their admission guide was incredibly helpful in preparing my application."
              </p>
            </div>
            <div className="testimonial-author">
              <img 
                src="https://randomuser.me/api/portraits/women/32.jpg" 
                alt="Sarah Johnson" 
                className="testimonial-avatar" 
              />
              <div className="testimonial-info">
                <h4>Sarah Johnson</h4>
                <p>University of Edinburgh, UK</p>
              </div>
            </div>
          </div>

          <div className="testimonial-card">
            <div className="testimonial-content">
              <p>
                "I found a full scholarship through Go Abroad's platform. 
                Their team guided me through every step of the application process."
              </p>
            </div>
            <div className="testimonial-author">
              <img 
                src="https://randomuser.me/api/portraits/men/45.jpg" 
                alt="Michael Chen" 
                className="testimonial-avatar" 
              />
              <div className="testimonial-info">
                <h4>Michael Chen</h4>
                <p>Stanford University, USA</p>
              </div>
            </div>
          </div>

          <div className="testimonial-card">
            <div className="testimonial-content">
              <p>
                "The detailed information about visa requirements and language tests 
                saved me so much time and confusion. Highly recommended!"
              </p>
            </div>
            <div className="testimonial-author">
              <img 
                src="https://randomuser.me/api/portraits/women/68.jpg" 
                alt="Priya Sharma" 
                className="testimonial-avatar" 
              />
              <div className="testimonial-info">
                <h4>Priya Sharma</h4>
                <p>University of Toronto, Canada</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
