<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admission Guide - Go Abroad</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/modern-styles.css">
    <style>
        /* Page-specific styles */
        .page-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 50%, var(--accent) 100%);
            color: var(--white);
            padding: 6rem 0 3rem;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
            opacity: 0.4;
            z-index: 0;
        }

        .page-header-content {
            position: relative;
            z-index: 1;
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(to right, var(--white), rgba(255, 255, 255, 0.8));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            display: inline-block;
        }

        .page-description {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            max-width: 700px;
        }

        .filter-section {
            background-color: var(--white);
            padding: 1.5rem;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            margin-bottom: 2rem;
            transition: transform 0.3s var(--transition-ease);
        }

        .filter-section:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .filter-section h3 {
            margin-bottom: 1.25rem;
            color: var(--gray-900);
            font-weight: 600;
            position: relative;
            display: inline-block;
            padding-bottom: 0.5rem;
        }

        .filter-section h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            border-radius: 3px;
        }

        .filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
        }

        .filter-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            padding: 0.6rem 1.25rem;
            background-color: var(--gray-100);
            border-radius: var(--radius-full);
            transition: all 0.3s var(--transition-ease);
            font-weight: 500;
        }

        .filter-checkbox:hover {
            background-color: var(--primary-light);
            transform: translateY(-2px);
        }

        .filter-checkbox input[type="checkbox"] {
            accent-color: var(--primary);
        }

        .region-selector {
            margin-bottom: 2rem;
        }

        .region-selector h3 {
            margin-bottom: 1.25rem;
            color: var(--gray-900);
            font-weight: 600;
            position: relative;
            display: inline-block;
            padding-bottom: 0.5rem;
        }

        .region-selector h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            border-radius: 3px;
        }

        .region-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            background-color: var(--white);
            border-radius: var(--radius-lg);
            padding: 1rem;
            box-shadow: var(--shadow-md);
        }

        .region-tab {
            background-color: var(--gray-100);
            color: var(--gray-700);
            border: 2px solid transparent;
            padding: 0.85rem 1.5rem;
            border-radius: var(--radius-full);
            cursor: pointer;
            transition: all 0.3s var(--transition-ease);
            font-weight: 600;
            font-size: 0.95rem;
            flex: 1;
            min-width: 120px;
            text-align: center;
            outline: none;
            font-family: inherit;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .region-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            opacity: 0;
            transition: opacity 0.3s var(--transition-ease);
            z-index: -1;
        }

        .region-tab:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            color: var(--primary);
        }

        .region-tab.active {
            color: var(--white);
            border-color: transparent;
            box-shadow: var(--shadow-md);
        }

        .region-tab.active::before {
            opacity: 1;
        }

        .country-selector {
            margin-bottom: 2rem;
        }

        .country-selector h3 {
            margin-bottom: 1.25rem;
            color: var(--gray-900);
            font-weight: 600;
            position: relative;
            display: inline-block;
            padding-bottom: 0.5rem;
        }

        .country-selector h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            border-radius: 3px;
        }

        .country-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 0.75rem;
        }

        .country-btn {
            background-color: var(--gray-100);
            border: 2px solid transparent;
            border-radius: var(--radius-md);
            padding: 0.75rem;
            cursor: pointer;
            transition: all 0.3s var(--transition-ease);
            font-weight: 500;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .country-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            opacity: 0;
            transition: opacity 0.3s var(--transition-ease);
            z-index: -1;
        }

        .country-btn:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            color: var(--primary);
        }

        .country-btn.active {
            color: var(--white);
            border-color: transparent;
        }

        .country-btn.active::before {
            opacity: 1;
        }

        .requirements-table-container {
            background-color: var(--white);
            padding: 2rem;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            margin-bottom: 3rem;
            transition: transform 0.3s var(--transition-ease);
        }

        .requirements-table-container:hover {
            box-shadow: var(--shadow-lg);
        }

        .requirements-table-container h2 {
            margin-bottom: 1.5rem;
            color: var(--gray-900);
            font-weight: 700;
            position: relative;
            display: inline-block;
            padding-bottom: 0.5rem;
        }

        .requirements-table-container h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            border-radius: 3px;
        }

        .table-responsive {
            overflow-x: auto;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
        }

        .requirements-table {
            width: 100%;
            border-collapse: collapse;
        }

        .requirements-table th {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: var(--white);
            padding: 1rem 1.25rem;
            text-align: left;
            font-weight: 600;
        }

        .requirements-table th:first-child {
            border-top-left-radius: var(--radius-md);
        }

        .requirements-table th:last-child {
            border-top-right-radius: var(--radius-md);
        }

        .requirements-table tr {
            border-bottom: 1px solid var(--gray-200);
            transition: all 0.3s var(--transition-ease);
        }

        .requirements-table tr:hover {
            background-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--shadow-sm);
        }

        .requirements-table tr:last-child {
            border-bottom: none;
        }

        .requirements-table td {
            padding: 1.25rem;
            vertical-align: top;
        }

        .university-name {
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .university-location {
            font-size: 0.85rem;
            color: var(--gray-600);
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .university-location::before {
            content: '\f3c5';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: var(--primary);
            font-size: 0.8rem;
        }

        .program-ranking {
            font-weight: 700;
            color: var(--accent);
            margin-bottom: 0.75rem;
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background-color: var(--accent-light);
            border-radius: var(--radius-full);
            font-size: 0.85rem;
        }

        .programs-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .program-tag {
            background-color: var(--primary-light);
            color: var(--primary);
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: 0.8rem;
            font-weight: 500;
            transition: all 0.3s var(--transition-ease);
        }

        .program-tag:hover {
            background-color: var(--primary);
            color: var(--white);
            transform: translateY(-2px);
        }

        .test-requirement, .visa-info, .scholarship-info {
            margin-bottom: 0.5rem;
            position: relative;
            padding-left: 1.25rem;
            transition: all 0.3s var(--transition-ease);
        }

        .test-requirement:hover, .visa-info:hover, .scholarship-info:hover {
            transform: translateX(5px);
        }

        .test-requirement:before, .visa-info:before, .scholarship-info:before {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--primary);
        }

        .test-info-section {
            margin-top: 3rem;
        }

        .test-info-section h3 {
            margin-bottom: 1.5rem;
            color: var(--gray-900);
            font-weight: 600;
            position: relative;
            display: inline-block;
            padding-bottom: 0.5rem;
        }

        .test-info-section h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            border-radius: 3px;
        }

        .tests-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .test-card {
            background-color: var(--white);
            padding: 1.5rem;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            transition: all 0.3s var(--transition-ease);
        }

        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .test-card h4 {
            color: var(--primary);
            margin-bottom: 1rem;
            border-bottom: 1px solid var(--gray-200);
            padding-bottom: 0.75rem;
            font-weight: 600;
        }

        .test-card p {
            margin-bottom: 0.75rem;
            position: relative;
            padding-left: 1.25rem;
            transition: all 0.3s var(--transition-ease);
        }

        .test-card p:hover {
            transform: translateX(5px);
        }

        .test-card p:before {
            content: '\f0da';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--accent);
        }

        @media (max-width: 768px) {
            .filter-options {
                flex-direction: column;
                align-items: flex-start;
            }

            .country-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .region-tabs {
                flex-direction: column;
            }

            .region-tab {
                width: 100%;
            }

            .requirements-table th,
            .requirements-table td {
                padding: 0.75rem;
            }

            .test-card {
                padding: 1rem;
            }
        }

        @media (max-width: 576px) {
            .country-grid {
                grid-template-columns: 1fr;
            }

            .tests-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top bg-white">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-globe-americas"></i> Go Abroad
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="search_universities.html">Search Universities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="scholarships.html">Scholarships</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="applications.html">Applications</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="admission_guide.html">Admission Guide</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <header class="page-header">
        <div class="container">
            <div class="page-header-content" data-aos="fade-up">
                <h1 class="page-title">Admission Guide</h1>
                <p class="page-description">Welcome to the Admission Guide — your one-stop hub for country-wise university requirements across top global study destinations. Select a country to explore key admission requirements, accepted English tests, standardized exams, and work visa policies.</p>
            </div>
        </div>
    </header>

    <main>
        <div class="container mt-4">

        <div class="filter-section">
            <h3>Filter Universities</h3>
            <div class="filter-options">
                <label class="filter-checkbox">
                    <input type="checkbox" id="no-ielts"> No IELTS Required
                </label>
                <label class="filter-checkbox">
                    <input type="checkbox" id="duolingo-accepted"> Duolingo Accepted
                </label>
                <label class="filter-checkbox">
                    <input type="checkbox" id="pte-accepted"> PTE Accepted
                </label>
                <label class="filter-checkbox">
                    <input type="checkbox" id="no-gre"> No GRE/GMAT
                </label>
                <label class="filter-checkbox">
                    <input type="checkbox" id="low-tuition"> Tuition < 10K USD
                </label>
            </div>
        </div>

        <div class="region-selector">
            <h3>Select Region</h3>
            <div class="region-tabs">
                <button class="region-tab active" data-region="uk">🇬🇧 United Kingdom</button>
                <button class="region-tab" data-region="europe">🇪🇺 Europe</button>
                <button class="region-tab" data-region="usa">🇺🇸 United States</button>
                <button class="region-tab" data-region="canada">🇨🇦 Canada</button>
                <button class="region-tab" data-region="australia">🇦🇺 Australia</button>
            </div>
        </div>

        <div id="country-selector" class="country-selector" style="display: none;">
            <h3>Select Country</h3>
            <div class="country-grid" id="country-grid">
                <!-- Countries will be populated dynamically -->
            </div>
        </div>

        <div id="requirements-table-container" class="requirements-table-container">
            <h2>🇬🇧 United Kingdom - University Requirements</h2>

            <div class="table-responsive">
                <table class="requirements-table">
                    <thead>
                        <tr>
                            <th>University</th>
                            <th>Programs & Ranking</th>
                            <th>English Tests</th>
                            <th>GRE/GMAT</th>
                            <th>Visa/Work Permit</th>
                            <th>Scholarships</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="university-name">University of Oxford</div>
                                <div class="university-location">Oxford, England</div>
                            </td>
                            <td>
                                <div class="program-ranking">QS Rank: #4</div>
                                <div class="programs-list">
                                    <span class="program-tag">Business</span>
                                    <span class="program-tag">Law</span>
                                    <span class="program-tag">Medicine</span>
                                </div>
                            </td>
                            <td>
                                <div class="test-requirement">IELTS: 7.0-7.5</div>
                                <div class="test-requirement">TOEFL: 100+</div>
                                <div class="test-requirement">Cambridge: C1/C2</div>
                            </td>
                            <td>
                                <div class="test-requirement">GRE: Required for some programs</div>
                                <div class="test-requirement">GMAT: 650+ for MBA</div>
                            </td>
                            <td>
                                <div class="visa-info">Graduate Route: 2 years</div>
                                <div class="visa-info">Skilled Worker Visa possible</div>
                            </td>
                            <td>
                                <div class="scholarship-info">Rhodes Scholarship</div>
                                <div class="scholarship-info">Clarendon Fund</div>
                                <div class="scholarship-info">Department Scholarships</div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="university-name">Imperial College London</div>
                                <div class="university-location">London, England</div>
                            </td>
                            <td>
                                <div class="program-ranking">QS Rank: #7</div>
                                <div class="programs-list">
                                    <span class="program-tag">Engineering</span>
                                    <span class="program-tag">Medicine</span>
                                    <span class="program-tag">Science</span>
                                </div>
                            </td>
                            <td>
                                <div class="test-requirement">IELTS: 6.5-7.0</div>
                                <div class="test-requirement">TOEFL: 92+</div>
                                <div class="test-requirement">PTE: 62+</div>
                            </td>
                            <td>
                                <div class="test-requirement">GRE: Recommended</div>
                                <div class="test-requirement">GMAT: 600+ for Business</div>
                            </td>
                            <td>
                                <div class="visa-info">Graduate Route: 2 years</div>
                                <div class="visa-info">Skilled Worker Visa possible</div>
                            </td>
                            <td>
                                <div class="scholarship-info">President's Scholarship</div>
                                <div class="scholarship-info">Imperial College London Scholarships</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="test-info-section">
                <h3>English Language Tests</h3>
                <div class="tests-grid">
                    <div class="test-card">
                        <h4>IELTS Academic</h4>
                        <p>Most widely accepted test globally</p>
                        <p>Score range: 0-9.0 (band)</p>
                        <p>Validity: 2 years</p>
                        <p>Cost: ~$225 USD</p>
                    </div>
                    <div class="test-card">
                        <h4>TOEFL iBT</h4>
                        <p>Widely accepted in the US and Canada</p>
                        <p>Score range: 0-120</p>
                        <p>Validity: 2 years</p>
                        <p>Cost: ~$200 USD</p>
                    </div>
                    <div class="test-card">
                        <h4>Duolingo English Test</h4>
                        <p>Growing acceptance, online test</p>
                        <p>Score range: 10-160</p>
                        <p>Validity: 2 years</p>
                        <p>Cost: $49 USD</p>
                    </div>
                    <div class="test-card">
                        <h4>PTE Academic</h4>
                        <p>Computer-based test, quick results</p>
                        <p>Score range: 10-90</p>
                        <p>Validity: 2 years</p>
                        <p>Cost: ~$200 USD</p>
                    </div>
                </div>
            </div>

            <div class="test-info-section">
                <h3>Standardized Tests</h3>
                <div class="tests-grid">
                    <div class="test-card">
                        <h4>GRE (Graduate Record Examination)</h4>
                        <p>For graduate programs, especially in the US</p>
                        <p>Sections: Verbal, Quantitative, Analytical Writing</p>
                        <p>Validity: 5 years</p>
                        <p>Cost: $205 USD</p>
                    </div>
                    <div class="test-card">
                        <h4>GMAT (Graduate Management Admission Test)</h4>
                        <p>For business schools and MBA programs</p>
                        <p>Sections: Verbal, Quantitative, Integrated Reasoning, Analytical Writing</p>
                        <p>Validity: 5 years</p>
                        <p>Cost: $275 USD</p>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="footer-brand">
                        <i class="fas fa-globe-americas"></i> Go Abroad
                    </div>
                    <p class="footer-text">Your trusted partner for international education. We help students achieve their dreams of studying abroad.</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Quick Links</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="index.html">Home</a></li>
                        <li class="footer-link"><a href="search_universities.html">Universities</a></li>
                        <li class="footer-link"><a href="scholarships.html">Scholarships</a></li>
                        <li class="footer-link"><a href="applications.html">Applications</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Destinations</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="#">United Kingdom</a></li>
                        <li class="footer-link"><a href="#">United States</a></li>
                        <li class="footer-link"><a href="#">Canada</a></li>
                        <li class="footer-link"><a href="#">Australia</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Contact</h3>
                    <ul class="footer-links">
                        <li class="footer-contact"><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li class="footer-contact"><i class="fas fa-phone me-2"></i> +****************</li>
                        <li class="footer-contact"><i class="fas fa-map-marker-alt me-2"></i> New York, NY</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Go Abroad. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Handle navigation active states
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

            // Set active link based on current page
            const currentPage = window.location.pathname.split('/').pop() || 'index.html';
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if ((currentPage === 'index.html' && (href === '#' || href === 'index.html')) ||
                    (href === currentPage)) {
                    link.classList.add('active');
                }
            });

            // Mobile menu toggle
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            if (navbarToggler) {
                navbarToggler.addEventListener('click', function() {
                    navbarCollapse.classList.toggle('show');
                });
            }
        });

        // University data for different regions and countries
        const universityData = {
            'uk': {
                title: '🇬🇧 United Kingdom - University Requirements',
                universities: [
                    {
                        name: 'University of Oxford',
                        location: 'Oxford, England',
                        rank: 4,
                        programs: ['Business', 'Law', 'Medicine'],
                        englishTests: ['IELTS: 7.0-7.5', 'TOEFL: 100+', 'Cambridge: C1/C2'],
                        greGmat: ['GRE: Required for some programs', 'GMAT: 650+ for MBA'],
                        visa: ['Graduate Route: 2 years', 'Skilled Worker Visa possible'],
                        scholarships: ['Rhodes Scholarship', 'Clarendon Fund', 'Department Scholarships']
                    },
                    {
                        name: 'Imperial College London',
                        location: 'London, England',
                        rank: 7,
                        programs: ['Engineering', 'Medicine', 'Science'],
                        englishTests: ['IELTS: 6.5-7.0', 'TOEFL: 92+', 'PTE: 62+'],
                        greGmat: ['GRE: Recommended', 'GMAT: 600+ for Business'],
                        visa: ['Graduate Route: 2 years', 'Skilled Worker Visa possible'],
                        scholarships: ['President\'s Scholarship', 'Imperial College London Scholarships']
                    }
                ]
            },
            'europe': {
                countries: {
                    'germany': {
                        title: '🇩🇪 Germany - University Requirements',
                        universities: [
                            {
                                name: 'Technical University of Munich',
                                location: 'Munich, Germany',
                                rank: 50,
                                programs: ['Engineering', 'Computer Science', 'Physics'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 88+', 'TestDaF: 4x4'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['EU Blue Card eligible', '18-month job search visa'],
                                scholarships: ['DAAD Scholarships', 'Free tuition (public)', 'Merit-based aid']
                            },
                            {
                                name: 'Humboldt University of Berlin',
                                location: 'Berlin, Germany',
                                rank: 128,
                                programs: ['Social Sciences', 'Philosophy', 'History'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 80+', 'TestDaF: 4x4'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['EU Blue Card eligible', '18-month job search visa'],
                                scholarships: ['DAAD Scholarships', 'Free tuition (public)', 'Erasmus+ funding']
                            }
                        ]
                    },
                    'france': {
                        title: '🇫🇷 France - University Requirements',
                        universities: [
                            {
                                name: 'Sorbonne University',
                                location: 'Paris, France',
                                rank: 83,
                                programs: ['Literature', 'Medicine', 'Sciences'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 80+', 'DELF: C1'],
                                greGmat: ['GRE: Not required', 'GMAT: Required for MBA'],
                                visa: ['APS work permit', 'Talent Passport available'],
                                scholarships: ['Eiffel Excellence Scholarship', 'Campus France grants', 'Low tuition fees']
                            },
                            {
                                name: 'École Normale Supérieure',
                                location: 'Paris, France',
                                rank: 24,
                                programs: ['Physics', 'Mathematics', 'Philosophy'],
                                englishTests: ['IELTS: 7.0+', 'TOEFL: 100+', 'DELF: C2'],
                                greGmat: ['GRE: Recommended', 'GMAT: Not applicable'],
                                visa: ['APS work permit', 'Talent Passport available'],
                                scholarships: ['ENS Scholarships', 'Government funding', 'Highly competitive']
                            }
                        ]
                    },
                    'netherlands': {
                        title: '🇳🇱 Netherlands - University Requirements',
                        universities: [
                            {
                                name: 'University of Amsterdam',
                                location: 'Amsterdam, Netherlands',
                                rank: 55,
                                programs: ['Business Administration', 'Psychology', 'AI'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 92+', 'Cambridge: C1'],
                                greGmat: ['GRE: Not required', 'GMAT: Required for MBA'],
                                visa: ['Orientation year visa', 'Highly Skilled Migrant visa'],
                                scholarships: ['Holland Scholarship', 'University scholarships', 'EU vs Non-EU rates']
                            },
                            {
                                name: 'Erasmus University Rotterdam',
                                location: 'Rotterdam, Netherlands',
                                rank: 176,
                                programs: ['Business Administration', 'Economics', 'Medicine'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 90+', 'Cambridge: C1'],
                                greGmat: ['GRE: Not required', 'GMAT: Required for MBA'],
                                visa: ['Orientation year visa', 'Highly Skilled Migrant visa'],
                                scholarships: ['Erasmus Scholarships', 'Merit-based aid', 'Business focus']
                            }
                        ]
                    },
                    'spain': {
                        title: '🇪🇸 Spain - University Requirements',
                        universities: [
                            {
                                name: 'University of Barcelona',
                                location: 'Barcelona, Spain',
                                rank: 164,
                                programs: ['Architecture', 'Tourism', 'Medicine'],
                                englishTests: ['IELTS: 6.0+', 'TOEFL: 80+', 'DELE: B2'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['Student visa to work visa', 'EU residence permit'],
                                scholarships: ['Spanish government grants', 'Affordable tuition', 'Regional scholarships']
                            }
                        ]
                    },
                    'sweden': {
                        title: '🇸🇪 Sweden - University Requirements',
                        universities: [
                            {
                                name: 'Karolinska Institute',
                                location: 'Stockholm, Sweden',
                                rank: 10,
                                programs: ['Medicine', 'Biomedicine', 'Public Health'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 90+', 'Cambridge: C1'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['Work permit after graduation', 'EU residence permit'],
                                scholarships: ['Swedish Institute Scholarships', 'Free for EU students', 'Medical excellence']
                            },
                            {
                                name: 'Lund University',
                                location: 'Lund, Sweden',
                                rank: 85,
                                programs: ['Engineering', 'International Development', 'Business'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 90+', 'Cambridge: C1'],
                                greGmat: ['GRE: Not required', 'GMAT: Required for MBA'],
                                visa: ['Work permit after graduation', 'EU residence permit'],
                                scholarships: ['Lund University Scholarships', 'English programs', 'Merit-based aid']
                            }
                        ]
                    },
                    'switzerland': {
                        title: '🇨🇭 Switzerland - University Requirements',
                        universities: [
                            {
                                name: 'ETH Zurich',
                                location: 'Zurich, Switzerland',
                                rank: 7,
                                programs: ['Engineering', 'Physics', 'Computer Science'],
                                englishTests: ['IELTS: 7.0+', 'TOEFL: 100+', 'Cambridge: C2'],
                                greGmat: ['GRE: Recommended', 'GMAT: Not applicable'],
                                visa: ['Work permit available', 'EU/EFTA advantages'],
                                scholarships: ['ETH Excellence Scholarships', 'Low tuition fees', 'World-class research']
                            }
                        ]
                    },
                    'italy': {
                        title: '🇮🇹 Italy - University Requirements',
                        universities: [
                            {
                                name: 'Politecnico di Milano',
                                location: 'Milan, Italy',
                                rank: 123,
                                programs: ['Design', 'Architecture', 'Engineering'],
                                englishTests: ['IELTS: 6.0+', 'TOEFL: 78+', 'Cambridge: B2'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['Student to work visa', 'EU residence permit'],
                                scholarships: ['Italian government scholarships', 'Design excellence', 'Affordable tuition']
                            }
                        ]
                    },
                    'ireland': {
                        title: '🇮🇪 Ireland - University Requirements',
                        universities: [
                            {
                                name: 'Trinity College Dublin',
                                location: 'Dublin, Ireland',
                                rank: 81,
                                programs: ['Computer Science', 'Law', 'Business'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 90+', 'PTE: 63+'],
                                greGmat: ['GRE: Not required', 'GMAT: Required for MBA'],
                                visa: ['Stay Back Option: 2 years', 'EU work rights'],
                                scholarships: ['Trinity Scholarships', 'English speaking', 'EU advantages']
                            }
                        ]
                    },
                    'denmark': {
                        title: '🇩🇰 Denmark - University Requirements',
                        universities: [
                            {
                                name: 'University of Copenhagen',
                                location: 'Copenhagen, Denmark',
                                rank: 82,
                                programs: ['Environmental Science', 'Medicine', 'Economics'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 83+', 'Cambridge: C1'],
                                greGmat: ['GRE: Not required', 'GMAT: Required for MBA'],
                                visa: ['Work permit after graduation', 'EU residence permit'],
                                scholarships: ['Danish government scholarships', 'Free for EU students', 'Sustainability focus']
                            }
                        ]
                    },
                    'belgium': {
                        title: '🇧🇪 Belgium - University Requirements',
                        universities: [
                            {
                                name: 'KU Leuven',
                                location: 'Leuven, Belgium',
                                rank: 61,
                                programs: ['Engineering', 'Philosophy', 'Medicine'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 79+', 'Cambridge: C1'],
                                greGmat: ['GRE: Not required', 'GMAT: Required for MBA'],
                                visa: ['Work permit available', 'EU residence permit'],
                                scholarships: ['KU Leuven Scholarships', 'Low tuition fees', 'Research excellence']
                            }
                        ]
                    },
                    'austria': {
                        title: '🇦🇹 Austria - University Requirements',
                        universities: [
                            {
                                name: 'University of Vienna',
                                location: 'Vienna, Austria',
                                rank: 134,
                                programs: ['Humanities', 'Social Sciences', 'Psychology'],
                                englishTests: ['IELTS: 6.0+', 'TOEFL: 80+', 'ÖSD: C1'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['Red-White-Red Card', 'EU residence permit'],
                                scholarships: ['Austrian scholarships', 'Very low tuition', 'Cultural hub']
                            }
                        ]
                    },
                    'poland': {
                        title: '🇵🇱 Poland - University Requirements',
                        universities: [
                            {
                                name: 'University of Warsaw',
                                location: 'Warsaw, Poland',
                                rank: 262,
                                programs: ['Economics', 'Law', 'Political Science'],
                                englishTests: ['IELTS: 6.0+', 'TOEFL: 80+', 'Cambridge: B2'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['Work permit available', 'EU residence permit'],
                                scholarships: ['Polish government scholarships', 'Affordable option', 'EU membership benefits']
                            }
                        ]
                    },
                    'norway': {
                        title: '🇳🇴 Norway - University Requirements',
                        universities: [
                            {
                                name: 'University of Oslo',
                                location: 'Oslo, Norway',
                                rank: 117,
                                programs: ['Marine Biology', 'Energy Studies', 'Peace Studies'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 90+', 'Cambridge: C1'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['Work permit after graduation', 'EEA advantages'],
                                scholarships: ['Norwegian scholarships', 'No tuition fees', 'Free education']
                            }
                        ]
                    },
                    'finland': {
                        title: '🇫🇮 Finland - University Requirements',
                        universities: [
                            {
                                name: 'University of Helsinki',
                                location: 'Helsinki, Finland',
                                rank: 106,
                                programs: ['Education', 'IT', 'Environmental Science'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 92+', 'Cambridge: C1'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['Work permit after graduation', 'EU residence permit'],
                                scholarships: ['Finnish scholarships', 'Free for EU students', 'Education excellence']
                            }
                        ]
                    },
                    'portugal': {
                        title: '🇵🇹 Portugal - University Requirements',
                        universities: [
                            {
                                name: 'University of Lisbon',
                                location: 'Lisbon, Portugal',
                                rank: 356,
                                programs: ['Marine Sciences', 'Management', 'Architecture'],
                                englishTests: ['IELTS: 6.0+', 'TOEFL: 80+', 'Cambridge: B2'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['Work permit available', 'EU residence permit'],
                                scholarships: ['Portuguese scholarships', 'Low tuition fees', 'Coastal location']
                            }
                        ]
                    },
                    'greece': {
                        title: '🇬🇷 Greece - University Requirements',
                        universities: [
                            {
                                name: 'National Technical University of Athens',
                                location: 'Athens, Greece',
                                rank: 400,
                                programs: ['Engineering', 'Architecture', 'Technology'],
                                englishTests: ['IELTS: 6.0+', 'TOEFL: 80+', 'Cambridge: B2'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['Work permit available', 'EU residence permit'],
                                scholarships: ['Greek scholarships', 'Low tuition fees', 'Technical focus']
                            }
                        ]
                    },
                    'czech-republic': {
                        title: '🇨🇿 Czech Republic - University Requirements',
                        universities: [
                            {
                                name: 'Charles University',
                                location: 'Prague, Czech Republic',
                                rank: 266,
                                programs: ['Medicine', 'Law', 'Humanities'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 90+', 'Cambridge: C1'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['Work permit available', 'EU residence permit'],
                                scholarships: ['Czech scholarships', 'Moderate tuition', 'Historic city']
                            }
                        ]
                    },
                    'hungary': {
                        title: '🇭🇺 Hungary - University Requirements',
                        universities: [
                            {
                                name: 'Eötvös Loránd University',
                                location: 'Budapest, Hungary',
                                rank: 500,
                                programs: ['Psychology', 'Physics', 'Mathematics'],
                                englishTests: ['IELTS: 6.0+', 'TOEFL: 80+', 'Cambridge: B2'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['Work permit available', 'EU residence permit'],
                                scholarships: ['Hungarian scholarships', 'Affordable tuition', 'Central Europe']
                            }
                        ]
                    },
                    'turkey': {
                        title: '🇹🇷 Turkey - University Requirements',
                        universities: [
                            {
                                name: 'Boğaziçi University',
                                location: 'Istanbul, Turkey',
                                rank: 511,
                                programs: ['Engineering', 'Business', 'Social Sciences'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 80+', 'YDS: 80+'],
                                greGmat: ['GRE: Recommended', 'GMAT: Required for MBA'],
                                visa: ['Work permit available', 'Bridge to Europe/Asia'],
                                scholarships: ['Turkish scholarships', 'Affordable tuition', 'Strategic location']
                            }
                        ]
                    },
                    'iceland': {
                        title: '🇮🇸 Iceland - University Requirements',
                        universities: [
                            {
                                name: 'University of Iceland',
                                location: 'Reykjavik, Iceland',
                                rank: 400,
                                programs: ['Renewable Energy', 'Nordic Studies', 'Environmental Science'],
                                englishTests: ['IELTS: 6.5+', 'TOEFL: 79+', 'Cambridge: C1'],
                                greGmat: ['GRE: Not required', 'GMAT: Not required'],
                                visa: ['Work permit available', 'EEA advantages'],
                                scholarships: ['Icelandic scholarships', 'No tuition fees', 'Unique location']
                            }
                        ]
                    }
                }
            },
            'usa': {
                title: '🇺🇸 United States - University Requirements',
                universities: [
                    {
                        name: 'University of California, Los Angeles',
                        location: 'Los Angeles, California',
                        rank: 13,
                        programs: ['Computer Science', 'Business', 'Film'],
                        englishTests: ['IELTS: 7.0+', 'TOEFL: 100+', 'Duolingo: 120+'],
                        greGmat: ['GRE: 320+', 'GMAT: 650+ for MBA'],
                        visa: ['F-1 to H-1B pathway', 'OPT: 1-3 years'],
                        scholarships: ['Merit scholarships', 'Need-based aid', 'Research assistantships']
                    }
                ]
            },
            'canada': {
                title: '🇨🇦 Canada - University Requirements',
                universities: [
                    {
                        name: 'University of Toronto',
                        location: 'Toronto, Ontario',
                        rank: 21,
                        programs: ['Computer Science', 'Medicine', 'Business'],
                        englishTests: ['IELTS: 6.5+', 'TOEFL: 100+', 'CELPIP: 9+'],
                        greGmat: ['GRE: Recommended', 'GMAT: 650+ for MBA'],
                        visa: ['PGWP: up to 3 years', 'Express Entry pathway'],
                        scholarships: ['Canadian scholarships', 'Provincial funding', 'Research opportunities']
                    }
                ]
            },
            'australia': {
                title: '🇦🇺 Australia - University Requirements',
                universities: [
                    {
                        name: 'University of Melbourne',
                        location: 'Melbourne, Victoria',
                        rank: 14,
                        programs: ['Medicine', 'Law', 'Engineering'],
                        englishTests: ['IELTS: 6.5+', 'TOEFL: 79+', 'PTE: 58+'],
                        greGmat: ['GRE: Not required', 'GMAT: Required for MBA'],
                        visa: ['Post-study work visa: 2-4 years', 'Skilled migration pathway'],
                        scholarships: ['Australian scholarships', 'University funding', 'Research grants']
                    }
                ]
            }
        };

        // European countries mapping
        const europeanCountries = {
            'germany': '🇩🇪 Germany',
            'france': '🇫🇷 France',
            'netherlands': '🇳🇱 Netherlands',
            'spain': '🇪🇸 Spain',
            'sweden': '🇸🇪 Sweden',
            'switzerland': '🇨🇭 Switzerland',
            'italy': '🇮🇹 Italy',
            'ireland': '🇮🇪 Ireland',
            'denmark': '🇩🇰 Denmark',
            'belgium': '🇧🇪 Belgium',
            'austria': '🇦🇹 Austria',
            'poland': '🇵🇱 Poland',
            'norway': '🇳🇴 Norway',
            'finland': '🇫🇮 Finland',
            'portugal': '🇵🇹 Portugal',
            'greece': '🇬🇷 Greece',
            'czech-republic': '🇨🇿 Czech Republic',
            'hungary': '🇭🇺 Hungary',
            'turkey': '🇹🇷 Turkey',
            'iceland': '🇮🇸 Iceland'
        };

        // Region tab functionality
        document.querySelectorAll('.region-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                document.querySelectorAll('.region-tab').forEach(t => {
                    t.classList.remove('active');
                });

                // Add active class to clicked tab
                this.classList.add('active');

                const region = this.getAttribute('data-region');

                if (region === 'europe') {
                    showEuropeanCountries();
                } else {
                    hideCountrySelector();
                    showRegionUniversities(region);
                }
            });
        });

        function showEuropeanCountries() {
            const countrySelector = document.getElementById('country-selector');
            const countryGrid = document.getElementById('country-grid');

            // Show country selector
            countrySelector.style.display = 'block';

            // Clear and populate countries
            countryGrid.innerHTML = '';
            Object.entries(europeanCountries).forEach(([key, name]) => {
                const button = document.createElement('button');
                button.className = 'country-btn';
                button.setAttribute('data-country', key);
                button.textContent = name;
                button.addEventListener('click', () => showCountryUniversities(key));
                countryGrid.appendChild(button);
            });

            // Hide the requirements table initially
            document.getElementById('requirements-table-container').style.display = 'none';
        }

        function hideCountrySelector() {
            document.getElementById('country-selector').style.display = 'none';
            document.getElementById('requirements-table-container').style.display = 'block';
        }

        function showRegionUniversities(region) {
            const data = universityData[region];
            if (!data) return;

            updateUniversityTable(data.title, data.universities);
        }

        function showCountryUniversities(country) {
            // Remove active class from all country buttons
            document.querySelectorAll('.country-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to clicked button
            event.target.classList.add('active');

            const data = universityData.europe.countries[country];
            if (!data) return;

            // Show the requirements table
            document.getElementById('requirements-table-container').style.display = 'block';

            updateUniversityTable(data.title, data.universities);

            // Scroll to the table
            document.getElementById('requirements-table-container').scrollIntoView({ behavior: 'smooth' });
        }

        function updateUniversityTable(title, universities) {
            // Update title
            document.querySelector('#requirements-table-container h2').textContent = title;

            // Update table body
            const tbody = document.querySelector('.requirements-table tbody');
            tbody.innerHTML = '';

            universities.forEach(uni => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="university-name">${uni.name}</div>
                        <div class="university-location">${uni.location}</div>
                    </td>
                    <td>
                        <div class="program-ranking">QS Rank: #${uni.rank}</div>
                        <div class="programs-list">
                            ${uni.programs.map(program => `<span class="program-tag">${program}</span>`).join('')}
                        </div>
                    </td>
                    <td>
                        ${uni.englishTests.map(test => `<div class="test-requirement">${test}</div>`).join('')}
                    </td>
                    <td>
                        ${uni.greGmat.map(test => `<div class="test-requirement">${test}</div>`).join('')}
                    </td>
                    <td>
                        ${uni.visa.map(info => `<div class="visa-info">${info}</div>`).join('')}
                    </td>
                    <td>
                        ${uni.scholarships.map(scholarship => `<div class="scholarship-info">${scholarship}</div>`).join('')}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Filter functionality
        document.querySelectorAll('.filter-checkbox input').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // In a real application, you would filter the table based on the selected filters
                console.log(`Filter ${this.id} is ${this.checked ? 'checked' : 'unchecked'}`);
            });
        });

        // Initialize with UK data
        document.addEventListener('DOMContentLoaded', function() {
            showRegionUniversities('uk');
        });
    </script>
</body>
</html>
