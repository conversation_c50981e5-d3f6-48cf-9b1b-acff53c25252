<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .region-tabs {
            display: flex;
            gap: 0;
            margin: 20px 0;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .region-tab {
            background-color: #f5f7fa;
            color: #333;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            font-weight: 500;
            border-right: 1px solid #e0e0e0;
            flex: 1;
            text-align: center;
            font-size: 14px;
        }

        .region-tab:last-child {
            border-right: none;
        }

        .region-tab:hover {
            background-color: #e3f2fd;
        }

        .region-tab.active-now {
            background-color: #2196F3;
            color: #ffffff;
            font-weight: 600;
        }

        .content {
            margin: 20px 0;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <h1>Tab Test - Click any tab below</h1>

    <div class="region-tabs">
        <button class="region-tab active-now" onclick="selectTab(this, 'uk')">🇬🇧 UK</button>
        <button class="region-tab" onclick="selectTab(this, 'europe')">🇪🇺 Europe</button>
        <button class="region-tab" onclick="selectTab(this, 'usa')">🇺🇸 USA</button>
        <button class="region-tab" onclick="selectTab(this, 'canada')">🇨🇦 Canada</button>
        <button class="region-tab" onclick="selectTab(this, 'australia-nz')">🇦🇺 Australia & NZ</button>
    </div>

    <div class="content" id="content">
        <h2>UK Content</h2>
        <p>This is the UK content. Click other tabs to see them highlight.</p>
    </div>

    <script>
        function selectTab(clickedTab, region) {
            console.log('Tab clicked:', region);

            // Remove active-now class from all tabs
            document.querySelectorAll('.region-tab').forEach(tab => {
                tab.classList.remove('active-now');
            });

            // Add active-now class to clicked tab
            clickedTab.classList.add('active-now');

            // Update content
            document.getElementById('content').innerHTML = `
                <h2>${region.toUpperCase()} Content</h2>
                <p>This is the ${region} content. The tab should be highlighted in blue.</p>
            `;
        }
    </script>
</body>
</html>
