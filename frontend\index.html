<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Go Abroad - Your Gateway to Global Education</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Swiper JS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/modern-styles.css">
    <style>
        /* Header Banner Styles */
        .header-banner {
            background: linear-gradient(135deg, #0066CC 0%, #00c9a7 100%);
            padding: 10px 0;
            position: relative;
            overflow: hidden;
            z-index: 1000;
        }

        .header-banner-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            margin: 0;
            padding: 0 20px;
        }

        /* Scholarship Banner - Replaces Logo */
        .scholarship-banner {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px 20px;
            border-radius: 25px;
            color: #0066CC;
            font-weight: 700;
            font-size: 1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .scholarship-banner:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .scholarship-text {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .scholarship-text i {
            color: #FFD700;
            font-size: 1.2rem;
        }

        .scholarship-cta {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        .scholarship-cta:hover {
            color: white;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4);
        }

        /* Single Rotating Country Display */
        .country-rotator {
            width: 320px;
            height: 65px;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 32px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .country-rotator:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: scale(1.02);
        }

        .country-display {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            padding: 0 24px;
            gap: 18px;
            opacity: 0;
            transform: translateX(30px);
            transition: all 0.5s ease;
        }

        .country-display.active {
            opacity: 1;
            transform: translateX(0);
        }

        .country-display.exit {
            opacity: 0;
            transform: translateX(-30px);
        }

        .country-flag-single {
            width: 48px;
            height: 36px;
            border-radius: 8px;
            object-fit: contain;
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.4);
            background: white;
        }

        .country-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .country-code {
            color: white;
            font-weight: 800;
            font-size: 1.2rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            line-height: 1;
            letter-spacing: 0.5px;
        }

        .country-full-name {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.85rem;
            font-weight: 600;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
            line-height: 1;
        }

        /* Rotation indicator */
        .rotation-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 2px;
            background: linear-gradient(90deg, #FFD700, #FFA500);
            width: 0%;
            animation: progressBar 2s linear infinite;
        }

        @keyframes progressBar {
            0% { width: 0%; }
            100% { width: 100%; }
        }

        /* Responsive Header Banner */
        @media (max-width: 768px) {
            .header-banner {
                padding: 12px 0;
            }

            .header-banner-content {
                flex-direction: column;
                gap: 15px;
                padding: 12px 20px;
            }

            .scholarship-banner {
                order: 1;
                font-size: 0.9rem;
                padding: 10px 16px;
                gap: 12px;
            }

            .scholarship-cta {
                font-size: 0.8rem;
                padding: 6px 12px;
            }

            .country-rotator {
                order: 2;
                width: 280px;
                height: 60px;
            }

            .country-flag-single {
                width: 42px;
                height: 32px;
            }

            .country-code {
                font-size: 1.1rem;
            }

            .country-full-name {
                font-size: 0.8rem;
            }

            /* Adjust navigation for mobile */
            .navbar.fixed-top {
                top: 88px !important;
            }

            .hero-section {
                padding: 11rem 0 5rem;
            }
        }

        @media (max-width: 576px) {
            .header-banner {
                padding: 14px 0;
            }

            .scholarship-banner {
                font-size: 0.85rem;
                padding: 8px 14px;
                gap: 10px;
                flex-direction: column;
                text-align: center;
            }

            .scholarship-text {
                gap: 6px;
            }

            .scholarship-cta {
                font-size: 0.75rem;
                padding: 5px 10px;
            }

            .country-rotator {
                width: 240px;
                height: 55px;
            }

            .country-flag-single {
                width: 36px;
                height: 27px;
            }

            .country-code {
                font-size: 1rem;
            }

            .country-full-name {
                font-size: 0.75rem;
            }

            /* Adjust navigation for small mobile */
            .navbar.fixed-top {
                top: 102px !important;
            }

            .hero-section {
                padding: 12rem 0 5rem;
            }
        }

        /* Page-specific styles */
        .hero-section {
            position: relative;
            min-height: 100vh;
            padding: 9rem 0 5rem; /* Increased top padding for header banner */
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 50%, var(--accent) 100%);
            color: var(--white);
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
            opacity: 0.4;
            z-index: 0;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            line-height: 1.2;
            background: linear-gradient(to right, var(--white), rgba(255, 255, 255, 0.8));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2.5rem;
            opacity: 0.9;
            max-width: 600px;
        }

        .hero-image {
            position: relative;
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-2xl);
            transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
            transition: all 0.5s var(--transition-ease);
            height: 400px;
            width: 100%;
        }

        .hero-image:hover {
            transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
        }

        .hero-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.2), rgba(0, 201, 167, 0.2));
            z-index: 1;
            pointer-events: none;
        }

        /* University Slider Styles */
        .university-slider {
            width: 100%;
            height: 100%;
            border-radius: var(--radius-lg);
            overflow: hidden;
            /* Set aspect ratio to 16:9 */
            aspect-ratio: 16/9;
            position: relative;
            /* Ensure stability */
            transform: translate3d(0, 0, 0);
            /* Prevent unwanted interactions */
            touch-action: pan-y;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        .university-slide {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
            /* Maintain aspect ratio */
            aspect-ratio: 16/9;
            background-color: #f0f0f0; /* Placeholder color while loading */
            /* Remove pointer cursor from slides */
            cursor: default;
            /* Ensure content is centered */
            display: flex;
            align-items: center;
            justify-content: center;
            /* Prevent unwanted interactions */
            pointer-events: none;
        }

        /* Allow pointer events only on interactive elements */
        .university-slide-content,
        .university-btn,
        .university-btn-apply,
        .university-btn-bookmark,
        .university-logo {
            pointer-events: auto;
        }

        .university-slide-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            /* Prevent distortion */
            transform: translateZ(0);
            backface-visibility: hidden;
            -webkit-font-smoothing: subpixel-antialiased;
            will-change: transform;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
            transition: opacity 0.3s ease;
            /* Ensure image is centered */
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            margin: auto;
        }

        /* Remove hover effect on image to prevent misalignment */
        .university-slide:hover .university-slide-img {
            transform: none;
        }

        /* Hide slider until ready to prevent FOUC */
        .university-slider:not(.slider-ready) .swiper-slide:not(:first-child) {
            opacity: 0;
        }

        .university-slider.slider-ready .swiper-slide {
            opacity: 1;
            transition: opacity 0.5s ease;
        }

        /* Navigation arrows - fixed position and more prominent */
        .swiper-button-next, .swiper-button-prev {
            color: white;
            background: rgba(0, 0, 0, 0.5);
            width: 44px;
            height: 44px;
            border-radius: 50%;
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            transition: all 0.3s ease;
            /* Fixed position */
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            /* Add shadow for better visibility */
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            /* Ensure they're always visible */
            opacity: 0.8;
            /* Prevent unwanted interactions */
            pointer-events: auto;
        }

        .swiper-button-prev {
            left: 15px;
        }

        .swiper-button-next {
            right: 15px;
        }

        .swiper-button-next:hover, .swiper-button-prev:hover {
            background: rgba(0, 0, 0, 0.7);
            transform: translateY(-50%) scale(1.1);
            opacity: 1;
        }

        .swiper-button-next:after, .swiper-button-prev:after {
            font-size: 18px;
            font-weight: bold;
        }

        /* Progress indicator/pagination - more prominent */
        .swiper-pagination {
            position: absolute;
            bottom: 15px;
            left: 0;
            right: 0;
            z-index: 10;
            display: flex;
            justify-content: center;
            gap: 8px;
            pointer-events: auto;
        }

        /* Fix for Safari */
        @supports (-webkit-touch-callout: none) {
            .university-slide-img {
                height: 100% !important;
                width: 100% !important;
            }
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .university-slider {
                aspect-ratio: 4/3;
            }

            .university-slide {
                aspect-ratio: 4/3;
            }

            .university-logo {
                width: 50px;
                height: 50px;
            }

            .university-name {
                font-size: 20px;
            }

            .university-content-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .university-content-right {
                align-items: flex-start;
                text-align: left;
            }

            .university-location {
                justify-content: flex-start;
                margin-bottom: 10px;
            }

            .university-actions {
                flex-direction: row;
                align-items: center;
                margin-top: 15px;
            }

            .university-btn-bookmark {
                position: relative;
                top: 0;
                right: 0;
            }
        }

        @media (max-width: 576px) {
            .university-slider {
                aspect-ratio: 1/1;
            }

            .university-slide {
                aspect-ratio: 1/1;
            }

            .university-badge {
                font-size: 0.75rem;
                padding: 0.35rem 0.75rem;
            }

            .university-slide-content {
                padding: 15px;
            }

            .university-name {
                font-size: 18px;
            }

            .university-rank {
                font-size: 0.75rem;
            }

            .university-location {
                font-size: 14px;
            }

            .university-details {
                font-size: 14px;
            }

            .university-detail-item {
                font-size: 14px;
            }

            .university-btn, .university-btn-apply {
                font-size: 0.75rem;
                padding: 0.35rem 0.75rem;
                min-width: auto;
            }

            .university-btn-bookmark {
                width: 32px;
                height: 32px;
            }
        }

        /* University logo */
        .university-logo {
            position: absolute;
            top: 1rem;
            left: 1rem;
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            padding: 0.5rem;
            box-shadow: var(--shadow-md);
            z-index: 3;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .university-logo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .university-slide-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            /* Improved gradient for text readability */
            background: linear-gradient(to top,
                rgba(0,0,0,0.95) 0%,
                rgba(0,0,0,0.8) 30%,
                rgba(0,0,0,0.6) 60%,
                rgba(0,0,0,0.2) 85%,
                rgba(0,0,0,0) 100%);
            color: white;
            z-index: 2;
            /* Ensure content is stable */
            transform: translate3d(0, 0, 0);
            /* Prevent content from shifting during transitions */
            backface-visibility: hidden;
            -webkit-font-smoothing: subpixel-antialiased;
            /* Fixed width to prevent layout shifts */
            width: 100%;
            box-sizing: border-box;
            /* Ensure consistent height */
            min-height: 200px;
        }

        /* Remove hover animation to prevent misalignment */
        .university-slide:hover .university-slide-content {
            transform: translate3d(0, 0, 0);
        }

        .university-name {
            font-size: 24px;
            font-weight: 800;
            margin-bottom: 0.5rem;
            /* Improve text readability */
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            position: relative;
            z-index: 3;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            letter-spacing: -0.5px;
        }

        .university-rank {
            font-size: 0.875rem;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-full);
            font-weight: 600;
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }

        .university-content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
            position: relative;
            /* Ensure grid is stable */
            transform: translate3d(0, 0, 0);
            /* Fixed dimensions to prevent layout shifts */
            width: 100%;
            /* Prevent content from shifting during transitions */
            backface-visibility: hidden;
            -webkit-font-smoothing: subpixel-antialiased;
        }

        .university-content-left {
            display: flex;
            flex-direction: column;
            gap: 10px;
            /* Fixed width to prevent layout shifts */
            width: 100%;
            /* Ensure consistent padding */
            padding-right: 5px;
        }

        .university-content-right {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-end;
            text-align: right;
            /* Fixed width to prevent layout shifts */
            width: 100%;
            /* Ensure consistent padding */
            padding-left: 5px;
        }

        .university-divider {
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
            margin: 10px 0;
            width: 100%;
        }

        .university-location {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
            font-size: 16px;
            /* Improve text readability */
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
            position: relative;
            z-index: 3;
        }

        .university-location i {
            color: var(--secondary);
        }

        .university-details {
            display: flex;
            flex-direction: column;
            gap: 10px;
            font-size: 16px;
            font-weight: 400;
        }

        .university-detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .university-detail-item i {
            color: var(--secondary);
            font-size: 14px;
            width: 20px;
            text-align: center;
        }

        .university-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-full);
            font-weight: 600;
            font-size: 0.875rem;
            box-shadow: var(--shadow-md);
            z-index: 2;
            animation: pulse 2s infinite;
            /* Add backdrop blur for better visibility */
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }

        .university-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: flex-end;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
            }
        }

        .university-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: transparent;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-full);
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s var(--transition-ease);
            position: relative;
            z-index: 3;
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
        }

        .university-btn:hover {
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .university-btn-apply {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-full);
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s var(--transition-ease);
            box-shadow: var(--shadow-md);
            position: relative;
            z-index: 3;
            min-width: 120px;
            justify-content: center;
        }

        .university-btn-apply:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(135deg, var(--primary-dark), var(--secondary-dark));
            color: white;
        }

        .university-btn-bookmark {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 50%;
            transition: all 0.3s var(--transition-ease);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            z-index: 3;
            position: absolute;
            top: 20px;
            right: 0;
        }

        .university-btn-bookmark:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .university-btn-bookmark.active {
            background: var(--secondary);
            color: white;
        }

        .swiper-pagination-bullet {
            background: white;
            opacity: 0.5;
            width: 12px;
            height: 12px;
            display: inline-block;
            border-radius: 50%;
            margin: 0;
            cursor: pointer;
            transition: all 0.3s ease;
            pointer-events: auto;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .swiper-pagination-bullet:hover {
            opacity: 0.8;
            transform: scale(1.1);
        }

        .swiper-pagination-bullet-active {
            background: white;
            opacity: 1;
            transform: scale(1.2);
        }

        /* Progress bar for slide position */
        .swiper-progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
            width: 0%;
            z-index: 10;
            transition: width 0.1s linear;
            opacity: 0.8;
        }

        /* Mobile responsiveness for university slider */
        @media (max-width: 768px) {
            .hero-image {
                height: 300px;
            }

            .university-name {
                font-size: 1.25rem;
            }

            .university-location {
                font-size: 0.875rem;
            }

            .university-badge {
                font-size: 0.75rem;
                padding: 0.4rem 0.8rem;
            }
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn-hero-primary {
            background: var(--white);
            color: var(--primary);
            padding: 0.875rem 2rem;
            border-radius: var(--radius-full);
            font-weight: 600;
            box-shadow: var(--shadow-lg);
            transition: all 0.3s var(--transition-ease);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-hero-primary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
            color: var(--primary-dark);
        }

        .btn-hero-secondary {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: var(--white);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.875rem 2rem;
            border-radius: var(--radius-full);
            font-weight: 600;
            transition: all 0.3s var(--transition-ease);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-hero-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
            color: var(--white);
        }

        .destinations-section {
            background-color: var(--gray-50);
            padding: 5rem 0;
        }

        .section-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            display: inline-block;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: var(--gray-600);
            max-width: 700px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <!-- Header Banner -->
    <div class="header-banner">
        <div class="header-banner-content">
            <!-- Scholarship Banner - Replaces Logo -->
            <div class="scholarship-banner">
                <div class="scholarship-text">
                    <i class="fas fa-graduation-cap"></i>
                    <span>Scholarships Available – Apply Now!</span>
                </div>
                <a href="scholarships.html" class="scholarship-cta">Apply Now</a>
            </div>

            <!-- Single Rotating Country Display -->
            <div class="country-rotator" id="countryRotator">
                <!-- Countries will be dynamically inserted here -->
                <div class="rotation-progress"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top bg-white" style="top: 56px; z-index: 999;">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-globe-americas"></i> Go Abroad
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="search_universities.html">Search Universities</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="scholarships.html">Scholarships</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="applications.html">Applications</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admission_guide.html">Admission Guide</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 hero-content" data-aos="fade-right" data-aos-duration="1000">
                    <h1 class="hero-title">Your Gateway to <span style="color: var(--secondary);">Global Education</span></h1>
                    <p class="hero-subtitle">Discover top universities worldwide, find scholarships, and get step-by-step guidance for your study abroad journey.</p>
                    <div class="hero-buttons">
                        <a href="search_universities.html" class="btn-hero-primary">
                            <i class="fas fa-search"></i> Start Exploring
                        </a>
                        <a href="scholarships.html" class="btn-hero-secondary">
                            <i class="fas fa-graduation-cap"></i> Find Scholarships
                        </a>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                    <div class="hero-image">
                        <!-- Slider main container -->
                        <div class="swiper university-slider">
                            <!-- Additional required wrapper -->
                            <div class="swiper-wrapper">
                                <!-- Slides -->
                                <div class="swiper-slide university-slide">
                                    <img src="https://images.unsplash.com/photo-1607237138185-eedd9c632b0b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1920&h=1080&q=100" alt="Oxford University" class="university-slide-img" loading="eager" fetchpriority="high" decoding="sync">
                                    <div class="university-logo">
                                        <img src="https://upload.wikimedia.org/wikipedia/commons/f/ff/Oxford-University-Circlet.svg" alt="Oxford University Logo">
                                    </div>
                                    <div class="university-badge">🎓 Admissions Open</div>
                                    <div class="university-slide-content">
                                        <h3 class="university-name">University of Oxford <span class="university-rank">QS #2</span></h3>

                                        <div class="university-content-grid">
                                            <div class="university-content-left">
                                                <div class="university-details">
                                                    <div class="university-detail-item">
                                                        <i class="fas fa-graduation-cap"></i>
                                                        <span>🎓 Medicine, Law, Business</span>
                                                    </div>
                                                    <div class="university-detail-item">
                                                        <i class="fas fa-coins"></i>
                                                        <span>💸 £26,770 - £37,510/year</span>
                                                    </div>
                                                    <div class="university-detail-item">
                                                        <i class="far fa-calendar-alt"></i>
                                                        <span>⏳ Deadline: Oct 15, 2024</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="university-content-right">
                                                <div class="university-location">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <span>Oxford, United Kingdom 🇬🇧</span>
                                                </div>

                                                <div class="university-actions">
                                                    <a href="https://www.ox.ac.uk/admissions/undergraduate/applying-to-oxford" target="_blank" class="university-btn-apply">
                                                        <i class="fas fa-paper-plane"></i> Apply Now
                                                    </a>
                                                    <a href="search_universities.html" class="university-btn">
                                                        <i class="fas fa-info-circle"></i> View Details
                                                    </a>
                                                </div>
                                            </div>

                                            <button class="university-btn-bookmark" onclick="toggleBookmark(this, 'oxford')">
                                                <i class="far fa-bookmark"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="swiper-slide university-slide">
                                    <img src="https://images.unsplash.com/photo-1583373834259-46cc92173cb7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1920&h=1080&q=100" alt="Harvard University" class="university-slide-img" loading="lazy" decoding="async">
                                    <div class="university-logo">
                                        <img src="https://upload.wikimedia.org/wikipedia/commons/7/70/Harvard_University_logo.svg" alt="Harvard University Logo">
                                    </div>
                                    <div class="university-badge">🎓 Admissions Open</div>
                                    <div class="university-slide-content">
                                        <h3 class="university-name">Harvard University <span class="university-rank">QS #5</span></h3>

                                        <div class="university-content-grid">
                                            <div class="university-content-left">
                                                <div class="university-details">
                                                    <div class="university-detail-item">
                                                        <i class="fas fa-graduation-cap"></i>
                                                        <span>🎓 Business, Law, Computer Science</span>
                                                    </div>
                                                    <div class="university-detail-item">
                                                        <i class="fas fa-coins"></i>
                                                        <span>💸 $54,768/year</span>
                                                    </div>
                                                    <div class="university-detail-item">
                                                        <i class="far fa-calendar-alt"></i>
                                                        <span>⏳ Deadline: Jan 1, 2025</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="university-content-right">
                                                <div class="university-location">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <span>Cambridge, USA 🇺🇸</span>
                                                </div>

                                                <div class="university-actions">
                                                    <a href="https://college.harvard.edu/admissions/apply" target="_blank" class="university-btn-apply">
                                                        <i class="fas fa-paper-plane"></i> Apply Now
                                                    </a>
                                                    <a href="search_universities.html" class="university-btn">
                                                        <i class="fas fa-info-circle"></i> View Details
                                                    </a>
                                                </div>
                                            </div>

                                            <button class="university-btn-bookmark" onclick="toggleBookmark(this, 'harvard')">
                                                <i class="far fa-bookmark"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="swiper-slide university-slide">
                                    <img src="https://images.unsplash.com/photo-1569447891824-7a1ef98d7f8f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1920&h=1080&q=100" alt="University of Toronto" class="university-slide-img" loading="lazy" decoding="async">
                                    <div class="university-logo">
                                        <img src="https://upload.wikimedia.org/wikipedia/commons/1/1a/University_of_Toronto_shield.svg" alt="University of Toronto Logo">
                                    </div>
                                    <div class="university-badge">🎓 Admissions Open</div>
                                    <div class="university-slide-content">
                                        <h3 class="university-name">University of Toronto <span class="university-rank">QS #21</span></h3>

                                        <div class="university-content-grid">
                                            <div class="university-content-left">
                                                <div class="university-details">
                                                    <div class="university-detail-item">
                                                        <i class="fas fa-graduation-cap"></i>
                                                        <span>🎓 Engineering, Medicine, Arts</span>
                                                    </div>
                                                    <div class="university-detail-item">
                                                        <i class="fas fa-coins"></i>
                                                        <span>💸 CAD $45,900 - $60,440/year</span>
                                                    </div>
                                                    <div class="university-detail-item">
                                                        <i class="far fa-calendar-alt"></i>
                                                        <span>⏳ Deadline: Jan 15, 2025</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="university-content-right">
                                                <div class="university-location">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <span>Toronto, Canada 🇨🇦</span>
                                                </div>

                                                <div class="university-actions">
                                                    <a href="https://future.utoronto.ca/apply/" target="_blank" class="university-btn-apply">
                                                        <i class="fas fa-paper-plane"></i> Apply Now
                                                    </a>
                                                    <a href="search_universities.html" class="university-btn">
                                                        <i class="fas fa-info-circle"></i> View Details
                                                    </a>
                                                </div>
                                            </div>

                                            <button class="university-btn-bookmark" onclick="toggleBookmark(this, 'toronto')">
                                                <i class="far fa-bookmark"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="swiper-slide university-slide">
                                    <img src="https://images.unsplash.com/photo-1612977512598-3b8d6a363e27?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1920&h=1080&q=100" alt="University of Melbourne" class="university-slide-img" loading="lazy" decoding="async">
                                    <div class="university-logo">
                                        <img src="https://upload.wikimedia.org/wikipedia/en/1/10/University_of_Melbourne_crest.svg" alt="University of Melbourne Logo">
                                    </div>
                                    <div class="university-badge">🎓 Admissions Open</div>
                                    <div class="university-slide-content">
                                        <h3 class="university-name">University of Melbourne <span class="university-rank">QS #14</span></h3>

                                        <div class="university-content-grid">
                                            <div class="university-content-left">
                                                <div class="university-details">
                                                    <div class="university-detail-item">
                                                        <i class="fas fa-graduation-cap"></i>
                                                        <span>🎓 Science, Arts, Business</span>
                                                    </div>
                                                    <div class="university-detail-item">
                                                        <i class="fas fa-coins"></i>
                                                        <span>💸 AUD $42,000 - $56,000/year</span>
                                                    </div>
                                                    <div class="university-detail-item">
                                                        <i class="far fa-calendar-alt"></i>
                                                        <span>⏳ Deadline: Dec 31, 2024</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="university-content-right">
                                                <div class="university-location">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <span>Melbourne, Australia 🇦🇺</span>
                                                </div>

                                                <div class="university-actions">
                                                    <a href="https://study.unimelb.edu.au/how-to-apply" target="_blank" class="university-btn-apply">
                                                        <i class="fas fa-paper-plane"></i> Apply Now
                                                    </a>
                                                    <a href="search_universities.html" class="university-btn">
                                                        <i class="fas fa-info-circle"></i> View Details
                                                    </a>
                                                </div>
                                            </div>

                                            <button class="university-btn-bookmark" onclick="toggleBookmark(this, 'melbourne')">
                                                <i class="far fa-bookmark"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="swiper-slide university-slide">
                                    <img src="https://images.unsplash.com/photo-1534609146522-5d8be2f339d3?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1920&h=1080&q=100" alt="ETH Zurich" class="university-slide-img" loading="lazy" decoding="async">
                                    <div class="university-logo">
                                        <img src="https://upload.wikimedia.org/wikipedia/commons/0/0d/ETH_Z%C3%BCrich_Logo.svg" alt="ETH Zurich Logo">
                                    </div>
                                    <div class="university-badge">🎓 Admissions Open</div>
                                    <div class="university-slide-content">
                                        <h3 class="university-name">ETH Zurich <span class="university-rank">QS #9</span></h3>

                                        <div class="university-content-grid">
                                            <div class="university-content-left">
                                                <div class="university-details">
                                                    <div class="university-detail-item">
                                                        <i class="fas fa-graduation-cap"></i>
                                                        <span>🎓 Engineering, Computer Science, Physics</span>
                                                    </div>
                                                    <div class="university-detail-item">
                                                        <i class="fas fa-coins"></i>
                                                        <span>💸 CHF 1,460/year</span>
                                                    </div>
                                                    <div class="university-detail-item">
                                                        <i class="far fa-calendar-alt"></i>
                                                        <span>⏳ Deadline: Apr 30, 2025</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="university-content-right">
                                                <div class="university-location">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <span>Zurich, Switzerland 🇨🇭</span>
                                                </div>

                                                <div class="university-actions">
                                                    <a href="https://ethz.ch/en/studies/registration-application.html" target="_blank" class="university-btn-apply">
                                                        <i class="fas fa-paper-plane"></i> Apply Now
                                                    </a>
                                                    <a href="search_universities.html" class="university-btn">
                                                        <i class="fas fa-info-circle"></i> View Details
                                                    </a>
                                                </div>
                                            </div>

                                            <button class="university-btn-bookmark" onclick="toggleBookmark(this, 'eth')">
                                                <i class="far fa-bookmark"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Pagination -->
                            <div class="swiper-pagination"></div>

                            <!-- Navigation arrows -->
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>

                            <!-- Progress bar -->
                            <div class="swiper-progress-bar"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Destinations -->
    <section class="destinations-section">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">Popular Study Destinations</h2>
                <p class="section-subtitle">Explore top countries for international education with world-class universities</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="card destination-card">
                        <div class="card-img-container">
                            <img src="https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80" class="card-img" alt="United Kingdom">
                            <div class="destination-flag">🇬🇧</div>
                            <div class="img-overlay"></div>
                        </div>
                        <div class="card-body">
                            <h3 class="card-title">United Kingdom</h3>
                            <p class="card-text">150+ Universities • Top Ranked</p>
                            <div class="university-tags mb-3">
                                <span class="university-tag">Oxford</span>
                                <span class="university-tag">Cambridge</span>
                                <span class="university-tag">Imperial</span>
                            </div>
                            <a href="search_universities.html" class="btn btn-primary w-100">
                                Explore UK <i class="fas fa-arrow-right ms-2"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="card destination-card">
                        <div class="card-img-container">
                            <img src="https://images.unsplash.com/photo-1485738422979-f5c462d49f74?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80" class="card-img" alt="United States">
                            <div class="destination-flag">🇺🇸</div>
                            <div class="img-overlay"></div>
                        </div>
                        <div class="card-body">
                            <h3 class="card-title">United States</h3>
                            <p class="card-text">200+ Universities • Ivy League</p>
                            <div class="university-tags mb-3">
                                <span class="university-tag">Harvard</span>
                                <span class="university-tag">MIT</span>
                                <span class="university-tag">Stanford</span>
                            </div>
                            <a href="search_universities.html" class="btn btn-primary w-100">
                                Explore USA <i class="fas fa-arrow-right ms-2"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="card destination-card">
                        <div class="card-img-container">
                            <img src="https://images.unsplash.com/photo-1549144511-f099e773c147?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80" class="card-img" alt="Canada">
                            <div class="destination-flag">🇨🇦</div>
                            <div class="img-overlay"></div>
                        </div>
                        <div class="card-body">
                            <h3 class="card-title">Canada</h3>
                            <p class="card-text">100+ Universities • Quality Education</p>
                            <div class="university-tags mb-3">
                                <span class="university-tag">Toronto</span>
                                <span class="university-tag">McGill</span>
                                <span class="university-tag">UBC</span>
                            </div>
                            <a href="search_universities.html" class="btn btn-primary w-100">
                                Explore Canada <i class="fas fa-arrow-right ms-2"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us -->
    <section class="features-section py-5">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">Why Choose Go Abroad?</h2>
                <p class="section-subtitle">We provide comprehensive support for your international education journey</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <div class="feature-content">
                            <h3 class="feature-title">Top Universities</h3>
                            <p class="feature-text">Access to 500+ world-renowned universities across multiple countries with detailed information and rankings.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="feature-content">
                            <h3 class="feature-title">Scholarship Support</h3>
                            <p class="feature-text">Find and apply for scholarships worth millions of dollars to fund your international education dreams.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div class="feature-content">
                            <h3 class="feature-title">Expert Guidance</h3>
                            <p class="feature-text">Get step-by-step guidance from admission experts and successful international students.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Student Testimonials -->
    <section class="testimonials-section py-5">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">Success Stories</h2>
                <p class="section-subtitle">Hear from students who achieved their dreams with Go Abroad</p>
            </div>
            <div class="testimonials-slider" data-aos="fade-up">
                <div class="row g-4">
                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <div class="quote-icon">
                                    <i class="fas fa-quote-right"></i>
                                </div>
                                <p class="testimonial-text">"Go Abroad helped me navigate the complex UK application process. I got into Oxford thanks to their detailed guidance on UCAS and scholarship applications!"</p>
                            </div>
                            <div class="testimonial-author">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Sarah Ahmed" class="author-image">
                                <div class="author-info">
                                    <h4 class="author-name">Sarah Ahmed</h4>
                                    <p class="author-university">Oxford University, UK</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <div class="quote-icon">
                                    <i class="fas fa-quote-right"></i>
                                </div>
                                <p class="testimonial-text">"The scholarship database was incredible! I found a full-ride scholarship to MIT that I never would have discovered otherwise. The platform saved me thousands of dollars."</p>
                            </div>
                            <div class="testimonial-author">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Ahmed Khan" class="author-image">
                                <div class="author-info">
                                    <h4 class="author-name">Ahmed Khan</h4>
                                    <p class="author-university">MIT, USA</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                        <div class="testimonial-card">
                            <div class="testimonial-content">
                                <div class="quote-icon">
                                    <i class="fas fa-quote-right"></i>
                                </div>
                                <p class="testimonial-text">"From university selection to visa approval, Go Abroad was with me every step. Now I'm studying at University of Toronto with a clear path to permanent residency!"</p>
                            </div>
                            <div class="testimonial-author">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Fatima Ali" class="author-image">
                                <div class="author-info">
                                    <h4 class="author-name">Fatima Ali</h4>
                                    <p class="author-university">University of Toronto, Canada</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta-section py-5">
        <div class="container">
            <div class="cta-container" data-aos="zoom-in">
                <div class="cta-content">
                    <h2 class="cta-title">Ready to Start Your Journey?</h2>
                    <p class="cta-text">Join thousands of students who have achieved their study abroad dreams</p>
                    <a href="search_universities.html" class="btn btn-cta">
                        <i class="fas fa-rocket me-2"></i> Get Started Today
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="footer-brand">
                        <i class="fas fa-globe-americas"></i> Go Abroad
                    </div>
                    <p class="footer-text">Your trusted partner for international education. We help students achieve their dreams of studying abroad.</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Quick Links</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="#">Home</a></li>
                        <li class="footer-link"><a href="search_universities.html">Universities</a></li>
                        <li class="footer-link"><a href="scholarships.html">Scholarships</a></li>
                        <li class="footer-link"><a href="applications.html">Applications</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Destinations</h3>
                    <ul class="footer-links">
                        <li class="footer-link"><a href="#">United Kingdom</a></li>
                        <li class="footer-link"><a href="#">United States</a></li>
                        <li class="footer-link"><a href="#">Canada</a></li>
                        <li class="footer-link"><a href="#">Australia</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3 class="footer-heading">Contact</h3>
                    <ul class="footer-links">
                        <li class="footer-contact"><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li class="footer-contact"><i class="fas fa-phone me-2"></i> +****************</li>
                        <li class="footer-contact"><i class="fas fa-map-marker-alt me-2"></i> New York, NY</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Go Abroad. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>

    <script>
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Initialize University Slider
        document.addEventListener('DOMContentLoaded', function() {
            // Preload images for smoother transitions
            const preloadImages = () => {
                const slides = document.querySelectorAll('.university-slide-img');
                slides.forEach(img => {
                    const src = img.getAttribute('src');
                    if (src) {
                        const preloadLink = document.createElement('link');
                        preloadLink.href = src;
                        preloadLink.rel = 'preload';
                        preloadLink.as = 'image';
                        document.head.appendChild(preloadLink);
                    }
                });
            };

            // Initialize Swiper
            const universitySwiper = new Swiper('.university-slider', {
                // Optional parameters
                loop: true,
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                },
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                    pauseOnMouseEnter: true,
                    waitForTransition: true
                },
                speed: 600,

                // Pagination
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                    renderBullet: function (index, className) {
                        return '<span class="' + className + '" role="button" aria-label="Go to slide ' + (index + 1) + '"></span>';
                    }
                },

                // Navigation arrows
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },

                // Improve loading behavior
                preloadImages: true,
                updateOnImagesReady: true,
                observer: true,
                observeParents: true,

                // Prevent image distortion
                preventInteractionOnTransition: true,

                // Wait for transition
                watchSlidesProgress: true,

                // Disable swiping/dragging to prevent misalignment
                allowTouchMove: false,
                simulateTouch: false,

                // Prevent clicks on slides from navigating
                noSwipingClass: 'university-slide',

                // Prevent keyboard navigation
                keyboard: false,

                // Prevent mousewheel navigation
                mousewheel: false,

                // Add responsive breakpoints
                breakpoints: {
                    // when window width is >= 320px
                    320: {
                        slidesPerView: 1
                    },
                    // when window width is >= 768px
                    768: {
                        slidesPerView: 1
                    }
                },

                // Add accessibility
                a11y: {
                    prevSlideMessage: 'Previous university',
                    nextSlideMessage: 'Next university',
                    firstSlideMessage: 'This is the first university',
                    lastSlideMessage: 'This is the last university',
                    paginationBulletMessage: 'Go to university {{index}}'
                },

                // Ensure images are loaded before transition
                on: {
                    beforeInit: function() {
                        // Force hardware acceleration
                        const slides = document.querySelectorAll('.university-slide-img');
                        slides.forEach(img => {
                            img.style.transform = 'translateZ(0)';
                            img.style.backfaceVisibility = 'hidden';
                            img.style.perspective = '1000px';

                            // Preload images
                            const imgSrc = img.getAttribute('src');
                            if (imgSrc) {
                                const preloadImg = new Image();
                                preloadImg.src = imgSrc;
                            }
                        });
                    },
                    imagesReady: function() {
                        this.el.classList.add('slider-ready');
                    },
                    slideChangeTransitionStart: function() {
                        const activeSlide = this.slides[this.activeIndex];
                        if (activeSlide) {
                            const img = activeSlide.querySelector('img');
                            if (img && !img.complete) {
                                img.style.opacity = '0';
                            }
                        }

                        // Update progress bar
                        const progressBar = document.querySelector('.swiper-progress-bar');
                        if (progressBar) {
                            progressBar.style.width = '0%';
                            setTimeout(() => {
                                progressBar.style.width = '100%';
                                progressBar.style.transition = 'width 5000ms linear';
                            }, 50);
                        }
                    },
                    slideChangeTransitionEnd: function() {
                        const activeSlide = this.slides[this.activeIndex];
                        if (activeSlide) {
                            const img = activeSlide.querySelector('img');
                            if (img) {
                                img.style.opacity = '1';
                            }
                        }
                    },
                    // Remove click handler to prevent navigation on slide clicks
                    click: null
                }
            });

            // Disable clicks on slides from navigating
            document.querySelectorAll('.university-slide').forEach(slide => {
                slide.addEventListener('click', function(e) {
                    // Only allow clicks on interactive elements
                    if (!e.target.closest('.university-btn') &&
                        !e.target.closest('.university-btn-apply') &&
                        !e.target.closest('.university-btn-bookmark') &&
                        !e.target.closest('.swiper-pagination') &&
                        !e.target.closest('.swiper-button-next') &&
                        !e.target.closest('.swiper-button-prev')) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                });
            });

            // Country Rotator Functionality
            function initializeCountryRotator() {
                const countries = [
                    // Europe
                    { code: 'UK', name: 'United Kingdom', flag: 'https://flagcdn.com/w320/gb.png', region: 'europe' },
                    { code: 'GER', name: 'Germany', flag: 'https://flagcdn.com/w320/de.png', region: 'europe' },
                    { code: 'FRA', name: 'France', flag: 'https://flagcdn.com/w320/fr.png', region: 'europe' },
                    { code: 'ITA', name: 'Italy', flag: 'https://flagcdn.com/w320/it.png', region: 'europe' },
                    { code: 'ESP', name: 'Spain', flag: 'https://flagcdn.com/w320/es.png', region: 'europe' },
                    { code: 'NLD', name: 'Netherlands', flag: 'https://flagcdn.com/w320/nl.png', region: 'europe' },
                    { code: 'SWE', name: 'Sweden', flag: 'https://flagcdn.com/w320/se.png', region: 'europe' },
                    { code: 'CHE', name: 'Switzerland', flag: 'https://flagcdn.com/w320/ch.png', region: 'europe' },
                    { code: 'NOR', name: 'Norway', flag: 'https://flagcdn.com/w320/no.png', region: 'europe' },
                    { code: 'DNK', name: 'Denmark', flag: 'https://flagcdn.com/w320/dk.png', region: 'europe' },
                    { code: 'FIN', name: 'Finland', flag: 'https://flagcdn.com/w320/fi.png', region: 'europe' },
                    { code: 'AUT', name: 'Austria', flag: 'https://flagcdn.com/w320/at.png', region: 'europe' },
                    { code: 'BEL', name: 'Belgium', flag: 'https://flagcdn.com/w320/be.png', region: 'europe' },
                    { code: 'IRL', name: 'Ireland', flag: 'https://flagcdn.com/w320/ie.png', region: 'europe' },
                    { code: 'POL', name: 'Poland', flag: 'https://flagcdn.com/w320/pl.png', region: 'europe' },
                    { code: 'PRT', name: 'Portugal', flag: 'https://flagcdn.com/w320/pt.png', region: 'europe' },
                    { code: 'GRC', name: 'Greece', flag: 'https://flagcdn.com/w320/gr.png', region: 'europe' },
                    { code: 'CZE', name: 'Czech Republic', flag: 'https://flagcdn.com/w320/cz.png', region: 'europe' },
                    { code: 'HUN', name: 'Hungary', flag: 'https://flagcdn.com/w320/hu.png', region: 'europe' },

                    // North America
                    { code: 'USA', name: 'United States', flag: 'https://flagcdn.com/w320/us.png', region: 'usa' },
                    { code: 'CAN', name: 'Canada', flag: 'https://flagcdn.com/w320/ca.png', region: 'canada' },

                    // Oceania
                    { code: 'AUS', name: 'Australia', flag: 'https://flagcdn.com/w320/au.png', region: 'australia' },
                    { code: 'NZL', name: 'New Zealand', flag: 'https://flagcdn.com/w320/nz.png', region: 'australia' },

                    // Asia
                    { code: 'JPN', name: 'Japan', flag: 'https://flagcdn.com/w320/jp.png', region: 'asia' },
                    { code: 'KOR', name: 'South Korea', flag: 'https://flagcdn.com/w320/kr.png', region: 'asia' },
                    { code: 'SGP', name: 'Singapore', flag: 'https://flagcdn.com/w320/sg.png', region: 'asia' },
                    { code: 'HKG', name: 'Hong Kong', flag: 'https://flagcdn.com/w320/hk.png', region: 'asia' },
                    { code: 'CHN', name: 'China', flag: 'https://flagcdn.com/w320/cn.png', region: 'asia' },
                    { code: 'IND', name: 'India', flag: 'https://flagcdn.com/w320/in.png', region: 'asia' },
                    { code: 'MYS', name: 'Malaysia', flag: 'https://flagcdn.com/w320/my.png', region: 'asia' },
                    { code: 'THA', name: 'Thailand', flag: 'https://flagcdn.com/w320/th.png', region: 'asia' },

                    // Middle East
                    { code: 'UAE', name: 'United Arab Emirates', flag: 'https://flagcdn.com/w320/ae.png', region: 'middle-east' },
                    { code: 'QAT', name: 'Qatar', flag: 'https://flagcdn.com/w320/qa.png', region: 'middle-east' },
                    { code: 'SAU', name: 'Saudi Arabia', flag: 'https://flagcdn.com/w320/sa.png', region: 'middle-east' },
                    { code: 'TUR', name: 'Turkey', flag: 'https://flagcdn.com/w320/tr.png', region: 'middle-east' },
                    { code: 'ISR', name: 'Israel', flag: 'https://flagcdn.com/w320/il.png', region: 'middle-east' },

                    // South America
                    { code: 'BRA', name: 'Brazil', flag: 'https://flagcdn.com/w320/br.png', region: 'south-america' },
                    { code: 'ARG', name: 'Argentina', flag: 'https://flagcdn.com/w320/ar.png', region: 'south-america' },
                    { code: 'CHL', name: 'Chile', flag: 'https://flagcdn.com/w320/cl.png', region: 'south-america' },

                    // Africa
                    { code: 'ZAF', name: 'South Africa', flag: 'https://flagcdn.com/w320/za.png', region: 'africa' },
                    { code: 'EGY', name: 'Egypt', flag: 'https://flagcdn.com/w320/eg.png', region: 'africa' },
                    { code: 'MAR', name: 'Morocco', flag: 'https://flagcdn.com/w320/ma.png', region: 'africa' }
                ];

                const rotator = document.getElementById('countryRotator');
                let currentIndex = 0;
                let rotationInterval;
                let isHovered = false;

                // Create country display elements
                countries.forEach((country, index) => {
                    const countryDisplay = document.createElement('div');
                    countryDisplay.className = 'country-display';
                    if (index === 0) countryDisplay.classList.add('active');

                    countryDisplay.innerHTML = `
                        <img src="${country.flag}" alt="${country.name} Flag" class="country-flag-single">
                        <div class="country-info">
                            <div class="country-code">${country.code}</div>
                            <div class="country-full-name">${country.name}</div>
                        </div>
                    `;

                    // Add click handler for navigation
                    countryDisplay.addEventListener('click', () => {
                        // Navigate to search universities page with country filter
                        window.location.href = `search_universities.html?country=${country.code.toLowerCase()}`;
                    });

                    rotator.appendChild(countryDisplay);
                });

                function rotateCountry() {
                    if (isHovered) return;

                    const currentDisplay = rotator.querySelector('.country-display.active');
                    if (currentDisplay) {
                        currentDisplay.classList.remove('active');
                        currentDisplay.classList.add('exit');

                        setTimeout(() => {
                            currentDisplay.classList.remove('exit');
                        }, 500);
                    }

                    currentIndex = (currentIndex + 1) % countries.length;
                    const nextDisplay = rotator.children[currentIndex];

                    setTimeout(() => {
                        nextDisplay.classList.add('active');
                    }, 250);
                }

                // Start rotation
                function startRotation() {
                    rotationInterval = setInterval(rotateCountry, 2000);
                }

                function stopRotation() {
                    clearInterval(rotationInterval);
                }

                // Pause on hover
                rotator.addEventListener('mouseenter', () => {
                    isHovered = true;
                    stopRotation();
                });

                rotator.addEventListener('mouseleave', () => {
                    isHovered = false;
                    startRotation();
                });

                // Start the rotation
                startRotation();
            }

            // Bookmark functionality
            function toggleBookmark(button, universityId) {
                // Toggle active class
                button.classList.toggle('active');

                // Change icon
                const icon = button.querySelector('i');
                if (button.classList.contains('active')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');

                    // Save to localStorage
                    saveBookmark(universityId);

                    // Show toast notification
                    showToast(`${universityId.charAt(0).toUpperCase() + universityId.slice(1)} University bookmarked!`);
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');

                    // Remove from localStorage
                    removeBookmark(universityId);

                    // Show toast notification
                    showToast(`${universityId.charAt(0).toUpperCase() + universityId.slice(1)} University removed from bookmarks`);
                }
            }

            function saveBookmark(universityId) {
                let bookmarks = JSON.parse(localStorage.getItem('bookmarkedUniversities')) || [];
                if (!bookmarks.includes(universityId)) {
                    bookmarks.push(universityId);
                    localStorage.setItem('bookmarkedUniversities', JSON.stringify(bookmarks));
                }
            }

            function removeBookmark(universityId) {
                let bookmarks = JSON.parse(localStorage.getItem('bookmarkedUniversities')) || [];
                bookmarks = bookmarks.filter(id => id !== universityId);
                localStorage.setItem('bookmarkedUniversities', JSON.stringify(bookmarks));
            }

            function showToast(message) {
                // Create toast element if it doesn't exist
                let toast = document.getElementById('bookmark-toast');
                if (!toast) {
                    toast = document.createElement('div');
                    toast.id = 'bookmark-toast';
                    toast.style.position = 'fixed';
                    toast.style.bottom = '20px';
                    toast.style.right = '20px';
                    toast.style.background = 'rgba(0, 0, 0, 0.8)';
                    toast.style.color = 'white';
                    toast.style.padding = '10px 20px';
                    toast.style.borderRadius = '4px';
                    toast.style.zIndex = '9999';
                    toast.style.transition = 'opacity 0.3s ease';
                    toast.style.opacity = '0';
                    document.body.appendChild(toast);
                }

                // Set message and show toast
                toast.textContent = message;
                toast.style.opacity = '1';

                // Hide toast after 3 seconds
                setTimeout(() => {
                    toast.style.opacity = '0';
                }, 3000);
            }

            // Initialize bookmarks from localStorage
            document.addEventListener('DOMContentLoaded', function() {
                const bookmarks = JSON.parse(localStorage.getItem('bookmarkedUniversities')) || [];

                // Update bookmark buttons based on localStorage
                bookmarks.forEach(universityId => {
                    const button = document.querySelector(`button[onclick*="${universityId}"]`);
                    if (button) {
                        button.classList.add('active');
                        const icon = button.querySelector('i');
                        icon.classList.remove('far');
                        icon.classList.add('fas');
                    }
                });
            });

            // Preload first few images
            preloadImages();

            // Initialize Country Rotator
            initializeCountryRotator();

            // Pause autoplay on hover
            const swiperContainer = document.querySelector('.university-slider');
            if (swiperContainer) {
                swiperContainer.addEventListener('mouseenter', function() {
                    universitySwiper.autoplay.stop();
                });

                swiperContainer.addEventListener('mouseleave', function() {
                    universitySwiper.autoplay.start();
                });
            }

            // Handle navigation active states
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

            // Set active link based on current page
            const currentPage = window.location.pathname.split('/').pop() || 'index.html';
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if ((currentPage === 'index.html' && (href === '#' || href === 'index.html')) ||
                    (href === currentPage)) {
                    link.classList.add('active');
                }
            });

            // Handle click events
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');
                });
            });

            // Mobile menu toggle
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            if (navbarToggler) {
                navbarToggler.addEventListener('click', function() {
                    navbarCollapse.classList.toggle('show');
                });
            }
        });
    </script>
</body>
</html>
