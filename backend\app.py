from flask import Flask, send_from_directory, jsonify, request
from flask_cors import CORS

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Comprehensive hierarchical data structure
universities_data = {
    'europe': {
        'germany': {
            'berlin': [
                {
                    'id': 'tu-berlin',
                    'name': 'Technical University of Berlin',
                    'logo': 'https://via.placeholder.com/80/1e88e5/ffffff?text=TUB',
                    'qs_rank': 154,
                    'programs': [
                        {'name': 'Computer Science', 'level': 'MSc', 'duration': '2 years'},
                        {'name': 'Robotics', 'level': 'MSc', 'duration': '2 years'},
                        {'name': 'Electrical Engineering', 'level': 'MSc', 'duration': '2 years'}
                    ],
                    'tuition': {'min': 0, 'max': 350, 'currency': 'EUR', 'note': 'Semester fee only'},
                    'language': ['English', 'German'],
                    'scholarships': True,
                    'deadline': '2025-07-15',
                    'requirements': {
                        'IELTS': '6.5',
                        'TOEFL': '90',
                        'TestDaF': '4'
                    },
                    'website': 'https://www.tu-berlin.de'
                },
                {
                    'id': 'humboldt-berlin',
                    'name': 'Humboldt University of Berlin',
                    'logo': 'https://via.placeholder.com/80/1e88e5/ffffff?text=HUB',
                    'qs_rank': 128,
                    'programs': [
                        {'name': 'Social Sciences', 'level': 'MA', 'duration': '2 years'},
                        {'name': 'Philosophy', 'level': 'MA', 'duration': '2 years'},
                        {'name': 'History', 'level': 'MA', 'duration': '2 years'}
                    ],
                    'tuition': {'min': 0, 'max': 350, 'currency': 'EUR', 'note': 'Semester fee only'},
                    'language': ['English', 'German'],
                    'scholarships': True,
                    'deadline': '2025-07-15',
                    'requirements': {
                        'IELTS': '6.5',
                        'TOEFL': '90',
                        'TestDaF': '4'
                    },
                    'website': 'https://www.hu-berlin.de'
                }
            ],
            'munich': [
                {
                    'id': 'tum',
                    'name': 'Technical University of Munich',
                    'logo': 'https://via.placeholder.com/80/1e88e5/ffffff?text=TUM',
                    'qs_rank': 50,
                    'programs': [
                        {'name': 'Computer Science', 'level': 'MSc', 'duration': '2 years'},
                        {'name': 'Mechanical Engineering', 'level': 'MSc', 'duration': '2 years'},
                        {'name': 'Data Engineering', 'level': 'MSc', 'duration': '2 years'}
                    ],
                    'tuition': {'min': 0, 'max': 350, 'currency': 'EUR', 'note': 'Semester fee only'},
                    'language': ['English', 'German'],
                    'scholarships': True,
                    'deadline': '2025-05-31',
                    'requirements': {
                        'IELTS': '6.5',
                        'TOEFL': '88',
                        'TestDaF': '4'
                    },
                    'website': 'https://www.tum.de'
                }
            ]
        },
        'france': {
            'paris': [
                {
                    'id': 'sorbonne',
                    'name': 'Sorbonne University',
                    'logo': 'https://via.placeholder.com/80/1e88e5/ffffff?text=SU',
                    'qs_rank': 83,
                    'programs': [
                        {'name': 'Literature', 'level': 'MA', 'duration': '2 years'},
                        {'name': 'Medicine', 'level': 'MD', 'duration': '6 years'},
                        {'name': 'Physics', 'level': 'MSc', 'duration': '2 years'}
                    ],
                    'tuition': {'min': 170, 'max': 3770, 'currency': 'EUR', 'note': 'EU vs non-EU rates'},
                    'language': ['English', 'French'],
                    'scholarships': True,
                    'deadline': '2025-03-15',
                    'requirements': {
                        'IELTS': '6.5',
                        'TOEFL': '90',
                        'DELF': 'C1'
                    },
                    'website': 'https://www.sorbonne-universite.fr'
                }
            ]
        },
        'netherlands': {
            'amsterdam': [
                {
                    'id': 'uva',
                    'name': 'University of Amsterdam',
                    'logo': 'https://via.placeholder.com/80/1e88e5/ffffff?text=UvA',
                    'qs_rank': 55,
                    'programs': [
                        {'name': 'Business Administration', 'level': 'MSc', 'duration': '1 year'},
                        {'name': 'Psychology', 'level': 'MSc', 'duration': '1 year'},
                        {'name': 'Computer Science', 'level': 'MSc', 'duration': '2 years'}
                    ],
                    'tuition': {'min': 2200, 'max': 15000, 'currency': 'EUR', 'note': 'EU vs non-EU rates'},
                    'language': ['English'],
                    'scholarships': True,
                    'deadline': '2025-04-01',
                    'requirements': {
                        'IELTS': '6.5',
                        'TOEFL': '92'
                    },
                    'website': 'https://www.uva.nl'
                }
            ]
        }
    },
    'usa': {
        'california': {
            'los-angeles': [
                {
                    'id': 'ucla',
                    'name': 'University of California, Los Angeles',
                    'logo': 'https://via.placeholder.com/80/1e88e5/ffffff?text=UCLA',
                    'qs_rank': 13,
                    'programs': [
                        {'name': 'Computer Science', 'level': 'MS', 'duration': '2 years'},
                        {'name': 'Business Administration', 'level': 'MBA', 'duration': '2 years'},
                        {'name': 'Film Studies', 'level': 'MFA', 'duration': '3 years'}
                    ],
                    'tuition': {'min': 45000, 'max': 60000, 'currency': 'USD', 'note': 'Per year'},
                    'language': ['English'],
                    'scholarships': True,
                    'deadline': '2024-12-15',
                    'requirements': {
                        'IELTS': '7.0',
                        'TOEFL': '100',
                        'GRE': '320+'
                    },
                    'website': 'https://www.ucla.edu'
                },
                {
                    'id': 'usc',
                    'name': 'University of Southern California',
                    'logo': 'https://via.placeholder.com/80/1e88e5/ffffff?text=USC',
                    'qs_rank': 116,
                    'programs': [
                        {'name': 'Engineering', 'level': 'MS', 'duration': '2 years'},
                        {'name': 'Cinema Arts', 'level': 'MFA', 'duration': '3 years'},
                        {'name': 'Business', 'level': 'MBA', 'duration': '2 years'}
                    ],
                    'tuition': {'min': 50000, 'max': 65000, 'currency': 'USD', 'note': 'Per year'},
                    'language': ['English'],
                    'scholarships': True,
                    'deadline': '2024-12-01',
                    'requirements': {
                        'IELTS': '6.5',
                        'TOEFL': '90',
                        'GRE': '310+'
                    },
                    'website': 'https://www.usc.edu'
                }
            ]
        },
        'texas': {
            'austin': [
                {
                    'id': 'ut-austin',
                    'name': 'University of Texas at Austin',
                    'logo': 'https://via.placeholder.com/80/1e88e5/ffffff?text=UT',
                    'qs_rank': 58,
                    'programs': [
                        {'name': 'Computer Science', 'level': 'MS', 'duration': '2 years'},
                        {'name': 'Business Administration', 'level': 'MBA', 'duration': '2 years'},
                        {'name': 'Engineering', 'level': 'MS', 'duration': '2 years'}
                    ],
                    'tuition': {'min': 35000, 'max': 50000, 'currency': 'USD', 'note': 'Per year'},
                    'language': ['English'],
                    'scholarships': True,
                    'deadline': '2024-12-15',
                    'requirements': {
                        'IELTS': '6.5',
                        'TOEFL': '79',
                        'GRE': '300+'
                    },
                    'website': 'https://www.utexas.edu'
                }
            ]
        }
    },
    'uk': {
        'england': {
            'london': [
                {
                    'id': 'ucl',
                    'name': 'University College London',
                    'logo': 'https://via.placeholder.com/80/1e88e5/ffffff?text=UCL',
                    'qs_rank': 8,
                    'programs': [
                        {'name': 'Computer Science', 'level': 'MSc', 'duration': '1 year'},
                        {'name': 'Medicine', 'level': 'MBBS', 'duration': '6 years'},
                        {'name': 'Law', 'level': 'LLM', 'duration': '1 year'}
                    ],
                    'tuition': {'min': 23000, 'max': 35000, 'currency': 'GBP', 'note': 'Per year'},
                    'language': ['English'],
                    'scholarships': True,
                    'deadline': '2025-01-15',
                    'requirements': {
                        'IELTS': '6.5',
                        'TOEFL': '92'
                    },
                    'website': 'https://www.ucl.ac.uk'
                },
                {
                    'id': 'imperial',
                    'name': 'Imperial College London',
                    'logo': 'https://via.placeholder.com/80/1e88e5/ffffff?text=ICL',
                    'qs_rank': 7,
                    'programs': [
                        {'name': 'Engineering', 'level': 'MSc', 'duration': '1 year'},
                        {'name': 'Medicine', 'level': 'MBBS', 'duration': '6 years'},
                        {'name': 'Business', 'level': 'MBA', 'duration': '1 year'}
                    ],
                    'tuition': {'min': 25000, 'max': 38000, 'currency': 'GBP', 'note': 'Per year'},
                    'language': ['English'],
                    'scholarships': True,
                    'deadline': '2025-02-28',
                    'requirements': {
                        'IELTS': '7.0',
                        'TOEFL': '100',
                        'GRE': 'Required for some programs'
                    },
                    'website': 'https://www.imperial.ac.uk'
                }
            ]
        },
        'scotland': {
            'edinburgh': [
                {
                    'id': 'edinburgh',
                    'name': 'University of Edinburgh',
                    'logo': 'https://via.placeholder.com/80/1e88e5/ffffff?text=UoE',
                    'qs_rank': 15,
                    'programs': [
                        {'name': 'Computer Science', 'level': 'MSc', 'duration': '1 year'},
                        {'name': 'Medicine', 'level': 'MBChB', 'duration': '6 years'},
                        {'name': 'Arts', 'level': 'MA', 'duration': '1 year'}
                    ],
                    'tuition': {'min': 26000, 'max': 37000, 'currency': 'GBP', 'note': 'Per year'},
                    'language': ['English'],
                    'scholarships': True,
                    'deadline': '2025-05-30',
                    'requirements': {
                        'IELTS': '6.5',
                        'TOEFL': '92',
                        'PTE': '62'
                    },
                    'website': 'https://www.ed.ac.uk'
                }
            ]
        }
    }
}

# Routes to serve static files
@app.route('/')
def index():
    return send_from_directory('../frontend', 'index.html')

@app.route('/<path:path>')
def serve_static(path):
    return send_from_directory('../frontend', path)

# API routes for hierarchical data
@app.route('/api/regions')
def get_regions():
    regions = {
        'europe': {'name': 'Europe', 'flag': '🇪🇺'},
        'usa': {'name': 'United States', 'flag': '🇺🇸'},
        'uk': {'name': 'United Kingdom', 'flag': '🇬🇧'}
    }
    return jsonify(regions)

@app.route('/api/regions/<region_id>/countries')
def get_countries(region_id):
    if region_id in universities_data:
        countries = {}
        for country_id, country_data in universities_data[region_id].items():
            countries[country_id] = {
                'name': country_id.replace('-', ' ').title(),
                'cities': list(country_data.keys())
            }
        return jsonify(countries)
    return jsonify({'error': 'Region not found'}), 404

@app.route('/api/regions/<region_id>/countries/<country_id>/cities')
def get_cities(region_id, country_id):
    if region_id in universities_data and country_id in universities_data[region_id]:
        cities = {}
        for city_id, universities in universities_data[region_id][country_id].items():
            cities[city_id] = {
                'name': city_id.replace('-', ' ').title(),
                'university_count': len(universities)
            }
        return jsonify(cities)
    return jsonify({'error': 'Country not found'}), 404

@app.route('/api/regions/<region_id>/countries/<country_id>/cities/<city_id>/universities')
def get_universities(region_id, country_id, city_id):
    if (region_id in universities_data and
        country_id in universities_data[region_id] and
        city_id in universities_data[region_id][country_id]):
        return jsonify(universities_data[region_id][country_id][city_id])
    return jsonify({'error': 'Universities not found'}), 404

@app.route('/api/search')
def search_universities():
    # Get query parameters
    tuition_min = request.args.get('tuition_min', type=int, default=0)
    tuition_max = request.args.get('tuition_max', type=int, default=100000)
    program_level = request.args.get('program_level', default='')
    language = request.args.get('language', default='')
    scholarships = request.args.get('scholarships', type=bool, default=False)

    results = []

    # Search through all universities
    for region_id, region_data in universities_data.items():
        for country_id, country_data in region_data.items():
            for city_id, universities in country_data.items():
                for university in universities:
                    # Apply filters
                    if university['tuition']['min'] < tuition_min or university['tuition']['max'] > tuition_max:
                        continue

                    if program_level and not any(prog['level'] == program_level for prog in university['programs']):
                        continue

                    if language and language not in university['language']:
                        continue

                    if scholarships and not university['scholarships']:
                        continue

                    # Add location info
                    university_result = university.copy()
                    university_result['location'] = {
                        'region': region_id,
                        'country': country_id,
                        'city': city_id
                    }
                    results.append(university_result)

    return jsonify(results)

if __name__ == '__main__':
    print("Starting Flask server...")
    print("Open http://localhost:5000 in your browser to view the application")
    app.run(debug=True, port=5000)
