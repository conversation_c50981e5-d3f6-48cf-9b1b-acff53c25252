import React from 'react';
import { FaTimes, FaStar, FaGraduationCap, FaMoneyBillWave, FaCalendarAlt, FaCheckCircle, FaExternalLinkAlt } from 'react-icons/fa';

const UniversityModal = ({ university, onClose }) => {
  // Prevent clicks inside the modal from closing it
  const handleModalClick = (e) => {
    e.stopPropagation();
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="university-modal" onClick={handleModalClick}>
        <button className="modal-close-btn" onClick={onClose}>
          <FaTimes />
        </button>
        
        <div className="modal-header">
          <div className="modal-university-logo">
            <img src={university.logo || 'https://via.placeholder.com/100'} alt={university.name} />
          </div>
          <div className="modal-university-info">
            <h2>{university.name}</h2>
            <div className="modal-university-ranking">
              <FaStar className="ranking-icon" />
              <span>QS World Ranking: {university.ranking}</span>
            </div>
          </div>
        </div>
        
        <div className="modal-body">
          <div className="modal-section">
            <h3>Programs</h3>
            <div className="programs-list">
              {university.programs ? (
                university.programs.map((program, index) => (
                  <div key={index} className="program-item">
                    <FaCheckCircle className="program-icon" />
                    <span>{program}</span>
                  </div>
                ))
              ) : (
                <p>Program information not available</p>
              )}
            </div>
          </div>
          
          <div className="modal-section">
            <h3>Tuition & Fees</h3>
            <div className="tuition-info">
              <FaMoneyBillWave className="tuition-icon" />
              <span>{university.tuition || 'Contact university for details'}</span>
            </div>
          </div>
          
          <div className="modal-section">
            <h3>Admission Requirements</h3>
            {university.requirements ? (
              <div className="requirements-list">
                {Object.entries(university.requirements).map(([test, score], index) => (
                  <div key={index} className="requirement-item">
                    <strong>{test}:</strong> {score}
                  </div>
                ))}
              </div>
            ) : (
              <p>Requirement information not available</p>
            )}
          </div>
          
          <div className="modal-section">
            <h3>Application Deadline</h3>
            <div className="deadline-info">
              <FaCalendarAlt className="deadline-icon" />
              <span>{university.deadline || 'Check university website for deadlines'}</span>
            </div>
          </div>
          
          <div className="modal-section">
            <h3>Scholarships</h3>
            <p>{university.scholarships ? 'Scholarships available for international students' : 'Scholarship information not available'}</p>
          </div>
        </div>
        
        <div className="modal-footer">
          <button className="apply-now-btn">
            Apply Now <FaExternalLinkAlt />
          </button>
          <button className="save-university-btn">
            Save to Favorites
          </button>
        </div>
      </div>
    </div>
  );
};

export default UniversityModal;
