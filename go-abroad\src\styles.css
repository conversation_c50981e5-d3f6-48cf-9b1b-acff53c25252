/* Global Variables */
:root {
  --primary-color: #1e88e5;
  --primary-dark: #1565c0;
  --primary-light: #64b5f6;
  --secondary-color: #ff9800;
  --secondary-dark: #f57c00;
  --secondary-light: #ffb74d;
  --accent-color: #4caf50;
  --white: #ffffff;
  --light-gray: #f5f7fa;
  --medium-gray: #e0e0e0;
  --dark-gray: #757575;
  --text-dark: #333333;
  --text-light: #666666;
  --error-color: #e53935;
  --success-color: #43a047;
  --warning-color: #ffa000;
  --border-radius: 8px;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: #f0f2f5;
  color: var(--text-dark);
  line-height: 1.6;
}

a {
  text-decoration: none;
  color: var(--primary-color);
}

ul {
  list-style: none;
}

button {
  cursor: pointer;
  font-family: 'Poppins', sans-serif;
}

/* App Layout */
.app {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  margin-left: 250px;
  transition: var(--transition);
  padding: 20px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.content-container {
  flex: 1;
}

/* Sidebar Styles */
.sidebar {
  width: 250px;
  background-color: var(--primary-color);
  color: var(--white);
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: var(--transition);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.sidebar-title {
  font-size: 1.2rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--white);
  font-size: 1.2rem;
  margin-left: auto;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.sidebar-nav-item {
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: var(--transition);
}

.sidebar-nav-item:hover, .sidebar-nav-item.active {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white);
}

.sidebar-icon {
  font-size: 1.2rem;
  min-width: 20px;
}

.sidebar-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.language-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.language-icon {
  font-size: 1.2rem;
}

.language-select {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar.collapsed .sidebar-title,
.sidebar.collapsed .sidebar-text,
.sidebar.collapsed .language-select {
  display: none;
}

.sidebar.collapsed .sidebar-toggle {
  margin-left: 0;
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

.mobile-toggle {
  display: none;
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  box-shadow: var(--shadow);
  z-index: 1001;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Page Titles */
.page-title {
  font-size: 2rem;
  margin-bottom: 10px;
  color: var(--primary-dark);
}

.page-description {
  font-size: 1.1rem;
  color: var(--text-light);
  margin-bottom: 30px;
}

/* Home Page Styles */
.home-container {
  padding: 20px 0;
}

.hero-section {
  display: flex;
  align-items: center;
  gap: 40px;
  margin-bottom: 60px;
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 40px;
  box-shadow: var(--shadow);
}

.hero-content {
  flex: 1;
}

.hero-content h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: var(--primary-dark);
}

.hero-content p {
  font-size: 1.1rem;
  margin-bottom: 30px;
  color: var(--text-light);
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
}

.cta-button {
  background-color: var(--secondary-color);
  color: var(--white);
  border: none;
  padding: 12px 25px;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: var(--transition);
}

.cta-button:hover {
  background-color: var(--secondary-dark);
  transform: translateY(-3px);
  box-shadow: var(--shadow-hover);
}

.features-section {
  margin-bottom: 60px;
}

.features-section h2 {
  font-size: 1.8rem;
  margin-bottom: 30px;
  text-align: center;
  color: var(--primary-dark);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-card {
  background-color: var(--white);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  transition: var(--transition);
  text-align: center;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-hover);
}

.feature-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: var(--primary-dark);
}

.feature-card p {
  color: var(--text-light);
  margin-bottom: 20px;
}

.feature-btn {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
  margin: 0 auto;
  transition: var(--transition);
}

.feature-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 60px;
}

.stat-card {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 30px;
  border-radius: var(--border-radius);
  text-align: center;
}

.stat-card h3 {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.testimonials-section {
  margin-bottom: 60px;
}

.testimonials-section h2 {
  font-size: 1.8rem;
  margin-bottom: 30px;
  text-align: center;
  color: var(--primary-dark);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.testimonial-card {
  background-color: var(--white);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.testimonial-content {
  margin-bottom: 20px;
  font-style: italic;
  color: var(--text-light);
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 15px;
}

.testimonial-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.testimonial-info h4 {
  font-size: 1.1rem;
  color: var(--primary-dark);
}

.testimonial-info p {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* Footer Styles */
.footer {
  background-color: var(--white);
  padding: 40px 20px 20px;
  margin-top: 40px;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-bottom: 30px;
}

.footer-section h3 {
  font-size: 1.2rem;
  margin-bottom: 20px;
  color: var(--primary-dark);
}

.footer-section p {
  margin-bottom: 10px;
  color: var(--text-light);
  display: flex;
  align-items: center;
  gap: 10px;
}

.footer-icon {
  color: var(--primary-color);
}

.social-icons {
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

.social-icon {
  width: 35px;
  height: 35px;
  background-color: var(--light-gray);
  color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.social-icon:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: var(--text-light);
  transition: var(--transition);
}

.footer-links a:hover {
  color: var(--primary-color);
}

.footer-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.footer-tag {
  background-color: var(--light-gray);
  color: var(--primary-color);
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid var(--medium-gray);
  color: var(--text-light);
  font-size: 0.9rem;
}

/* Search Universities Styles */
.tabs-container {
  padding: 20px 0;
}

.region-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 30px;
}

.region-tab {
  background-color: var(--light-gray);
  color: var(--text-dark);
  border: none;
  padding: 10px 20px;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
}

.region-tab:hover, .region-tab.active {
  background-color: var(--primary-color);
  color: var(--white);
}

.region-icon {
  font-size: 1.2rem;
}

.region-content {
  margin-bottom: 40px;
}

.university-list {
  margin-bottom: 30px;
}

.search-container {
  margin-bottom: 20px;
}

.search-input-wrapper {
  position: relative;
  max-width: 500px;
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--dark-gray);
}

.search-input {
  width: 100%;
  padding: 12px 15px 12px 45px;
  border: 1px solid var(--medium-gray);
  border-radius: 30px;
  font-size: 1rem;
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.2);
}

.country-section {
  margin-bottom: 30px;
}

.country-header {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 15px 20px;
  border-radius: var(--border-radius);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: var(--transition);
}

.country-header:hover {
  background-color: var(--primary-dark);
}

.country-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.toggle-icon {
  transition: var(--transition);
}

.country-header.expanded .toggle-icon {
  transform: rotate(90deg);
}

.universities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.university-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.university-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-hover);
}

.university-logo {
  text-align: center;
  margin-bottom: 15px;
}

.university-logo img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.university-name {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: var(--primary-dark);
  text-align: center;
}

.university-ranking, .university-programs, .university-tuition, .university-deadline {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 0.9rem;
  color: var(--text-light);
}

.ranking-icon, .programs-icon, .tuition-icon, .deadline-icon {
  color: var(--primary-color);
  min-width: 16px;
}

.university-requirements {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 15px;
}

.requirement-badge {
  background-color: var(--light-gray);
  color: var(--primary-color);
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
}

.view-details-btn {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 8px 15px;
  border-radius: var(--border-radius);
  width: 100%;
  transition: var(--transition);
}

.view-details-btn:hover {
  background-color: var(--primary-dark);
}

.no-results {
  text-align: center;
  padding: 30px;
  background-color: var(--light-gray);
  border-radius: var(--border-radius);
  color: var(--text-light);
}

/* University Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  padding: 20px;
}

.university-modal {
  background-color: var(--white);
  border-radius: var(--border-radius);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  color: var(--dark-gray);
  font-size: 1.2rem;
  cursor: pointer;
  z-index: 1;
}

.modal-header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border-bottom: 1px solid var(--medium-gray);
}

.modal-university-logo img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
}

.modal-university-info {
  flex: 1;
}

.modal-university-info h2 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: var(--primary-dark);
}

.modal-university-ranking {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-light);
}

.modal-body {
  padding: 20px;
}

.modal-section {
  margin-bottom: 20px;
}

.modal-section h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: var(--primary-dark);
  border-bottom: 1px solid var(--medium-gray);
  padding-bottom: 5px;
}

.programs-list, .requirements-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

.program-item, .requirement-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.program-icon {
  color: var(--success-color);
}

.tuition-info, .deadline-info {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-light);
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid var(--medium-gray);
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.apply-now-btn, .save-university-btn {
  padding: 10px 20px;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 10px;
}

.apply-now-btn {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  flex: 1;
}

.apply-now-btn:hover {
  background-color: var(--primary-dark);
}

.save-university-btn {
  background-color: var(--white);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  flex: 1;
}

.save-university-btn:hover {
  background-color: var(--light-gray);
}

/* Why Study Abroad Section */
.why-study-abroad {
  background-color: var(--white);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-top: 40px;
}

.why-study-abroad h2 {
  font-size: 1.8rem;
  margin-bottom: 15px;
  color: var(--primary-dark);
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.benefit-card {
  background-color: var(--light-gray);
  padding: 20px;
  border-radius: var(--border-radius);
  text-align: center;
  transition: var(--transition);
}

.benefit-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow);
}

.benefit-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.benefit-card h3 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: var(--primary-dark);
}

/* Admission Guide Styles */
.admission-guide {
  padding: 20px 0;
}

.search-filter-container {
  margin-bottom: 30px;
}

.filter-container {
  background-color: var(--light-gray);
  padding: 20px;
  border-radius: var(--border-radius);
  margin-top: 20px;
}

.filter-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.filter-icon {
  color: var(--primary-color);
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.country-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 30px;
}

.country-tab {
  background-color: var(--light-gray);
  color: var(--text-dark);
  border: none;
  padding: 10px 20px;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
}

.country-tab:hover, .country-tab.active {
  background-color: var(--primary-color);
  color: var(--white);
}

.country-requirements {
  background-color: var(--white);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 40px;
}

.country-requirements h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--primary-dark);
}

.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.requirement-card {
  background-color: var(--light-gray);
  padding: 20px;
  border-radius: var(--border-radius);
}

.requirement-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.requirement-icon {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.requirement-header h3 {
  font-size: 1.1rem;
  color: var(--primary-dark);
}

.requirement-list {
  list-style: none;
}

.requirement-list li {
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.test-info-section {
  margin-top: 40px;
}

.test-info-section h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--primary-dark);
}

.test-comparison-table {
  overflow-x: auto;
}

.test-comparison-table table {
  width: 100%;
  border-collapse: collapse;
}

.test-comparison-table th, .test-comparison-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--medium-gray);
}

.test-comparison-table th {
  background-color: var(--primary-color);
  color: var(--white);
}

.test-comparison-table tr:nth-child(even) {
  background-color: var(--light-gray);
}

/* Scholarships Styles */
.scholarships-container {
  padding: 20px 0;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-select {
  padding: 8px 15px;
  border: 1px solid var(--medium-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
}

.scholarships-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.scholarship-card {
  background-color: var(--white);
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.scholarship-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-hover);
}

.scholarship-name {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: var(--primary-dark);
}

.scholarship-detail {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.scholarship-icon {
  color: var(--primary-color);
  min-width: 16px;
}

.scholarship-description {
  margin: 15px 0;
  font-size: 0.9rem;
  color: var(--text-light);
}

.scholarship-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 15px;
}

.scholarship-field {
  background-color: var(--light-gray);
  color: var(--primary-color);
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
}

.scholarship-link {
  display: inline-block;
  background-color: var(--primary-color);
  color: var(--white);
  padding: 8px 15px;
  border-radius: var(--border-radius);
  text-align: center;
  transition: var(--transition);
}

.scholarship-link:hover {
  background-color: var(--primary-dark);
  color: var(--white);
}

.scholarship-tips {
  margin-top: 40px;
}

.scholarship-tips h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--primary-dark);
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
}

.tip-card {
  background-color: var(--white);
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.tip-card h3 {
  font-size: 1.1rem;
  margin-bottom: 10px;
  color: var(--primary-dark);
}

/* Applications Styles */
.applications-container {
  padding: 20px 0;
}

.applications-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.add-application-btn {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 10px 20px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  gap: 10px;
  transition: var(--transition);
}

.add-application-btn:hover {
  background-color: var(--primary-dark);
}

.new-application-form-container {
  background-color: var(--white);
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 30px;
}

.new-application-form-container h3 {
  font-size: 1.2rem;
  margin-bottom: 20px;
  color: var(--primary-dark);
}

.new-application-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--medium-gray);
  border-radius: var(--border-radius);
}

.form-buttons {
  grid-column: 1 / -1;
  display: flex;
  gap: 15px;
}

.submit-btn, .cancel-btn {
  padding: 10px 20px;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
}

.submit-btn {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
}

.submit-btn:hover {
  background-color: var(--primary-dark);
}

.cancel-btn {
  background-color: var(--white);
  color: var(--text-dark);
  border: 1px solid var(--medium-gray);
}

.cancel-btn:hover {
  background-color: var(--light-gray);
}

.applications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.application-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.application-header {
  padding: 15px 20px;
  background-color: var(--primary-color);
  color: var(--white);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.application-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.application-status {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
}

.status-icon {
  font-size: 1rem;
}

.status-icon.submitted {
  color: var(--success-color);
}

.status-icon.in-progress {
  color: var(--warning-color);
}

.status-icon.not-started {
  color: var(--error-color);
}

.application-details {
  padding: 20px;
  border-bottom: 1px solid var(--medium-gray);
}

.application-details p {
  margin-bottom: 10px;
}

.deadline {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--error-color);
}

.deadline-icon {
  color: var(--error-color);
}

.application-progress {
  padding: 20px;
  border-bottom: 1px solid var(--medium-gray);
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.progress-bar {
  height: 8px;
  background-color: var(--light-gray);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 4px;
}

.application-documents {
  padding: 20px;
  border-bottom: 1px solid var(--medium-gray);
}

.application-documents h4 {
  margin-bottom: 15px;
  font-size: 1rem;
  color: var(--primary-dark);
}

.documents-list {
  list-style: none;
}

.document-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid var(--light-gray);
}

.document-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.document-name {
  font-size: 0.9rem;
}

.document-status {
  font-size: 0.8rem;
  padding: 3px 8px;
  border-radius: 20px;
}

.document-status.uploaded {
  background-color: rgba(67, 160, 71, 0.1);
  color: var(--success-color);
}

.document-status.pending {
  background-color: rgba(255, 160, 0, 0.1);
  color: var(--warning-color);
}

.upload-btn {
  background-color: var(--light-gray);
  color: var(--primary-color);
  border: none;
  padding: 5px 10px;
  border-radius: var(--border-radius);
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: var(--transition);
}

.upload-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.application-actions {
  padding: 20px;
  display: flex;
  gap: 10px;
}

.view-details-btn, .submit-application-btn {
  padding: 8px 15px;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  transition: var(--transition);
  flex: 1;
}

.view-details-btn {
  background-color: var(--white);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.view-details-btn:hover {
  background-color: var(--light-gray);
}

.submit-application-btn {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
}

.submit-application-btn:hover {
  background-color: var(--primary-dark);
}

.no-applications {
  text-align: center;
  padding: 40px;
  background-color: var(--light-gray);
  border-radius: var(--border-radius);
}

.application-tips {
  margin-top: 40px;
}

.application-tips h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--primary-dark);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .hero-section {
    flex-direction: column;
    padding: 30px;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .modal-header {
    flex-direction: column;
    text-align: center;
  }

  .modal-footer {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 250px;
  }

  .sidebar.collapsed {
    transform: translateX(0);
    width: 250px;
  }

  .sidebar-toggle {
    display: block;
  }

  .sidebar-overlay {
    display: block;
  }

  .main-content {
    margin-left: 0;
  }

  .mobile-toggle {
    display: flex;
  }

  .page-title {
    font-size: 1.8rem;
  }

  .features-grid,
  .stats-section,
  .testimonials-grid,
  .footer-content,
  .requirements-grid,
  .tips-grid {
    grid-template-columns: 1fr;
  }

  .filter-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .applications-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .hero-content h1 {
    font-size: 1.8rem;
  }

  .hero-section {
    padding: 20px;
  }

  .feature-card,
  .testimonial-card,
  .university-card,
  .scholarship-card,
  .tip-card {
    padding: 20px;
  }

  .region-tabs,
  .country-tabs {
    flex-direction: column;
  }

  .filter-options {
    flex-direction: column;
    align-items: flex-start;
  }

  .new-application-form {
    grid-template-columns: 1fr;
  }
}
