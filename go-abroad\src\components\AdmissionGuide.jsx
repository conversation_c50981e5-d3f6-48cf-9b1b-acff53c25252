import React, { useState } from 'react';
import { FaFilter, FaSearch, FaGraduationCap, FaLanguage, FaPassport, FaMoneyBillWave, FaCalendarAlt } from 'react-icons/fa';

const AdmissionGuide = ({ data }) => {
  const [activeCountry, setActiveCountry] = useState('UK');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    noIELTS: false,
    duolingoAccepted: false,
    pteAccepted: false,
    noGRE: false,
    lowTuition: false
  });

  const handleFilterChange = (filter) => {
    setFilters({
      ...filters,
      [filter]: !filters[filter]
    });
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  // Filter the data based on search term
  const filteredData = {};
  Object.keys(data).forEach(country => {
    if (country.toLowerCase().includes(searchTerm.toLowerCase())) {
      filteredData[country] = data[country];
    }
  });

  return (
    <div className="admission-guide">
      <h1 className="page-title">Admission Guide</h1>
      <p className="page-description">
        Explore admission requirements for universities around the world. 
        Find information about English tests, standardized exams, and visa requirements.
      </p>

      <div className="search-filter-container">
        <div className="search-container">
          <div className="search-input-wrapper">
            <FaSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search countries..."
              value={searchTerm}
              onChange={handleSearch}
              className="search-input"
            />
          </div>
        </div>

        <div className="filter-container">
          <div className="filter-header">
            <FaFilter className="filter-icon" />
            <h3>Filter Requirements</h3>
          </div>
          
          <div className="filter-options">
            <label className="filter-option">
              <input
                type="checkbox"
                checked={filters.noIELTS}
                onChange={() => handleFilterChange('noIELTS')}
              />
              <span>No IELTS Required</span>
            </label>
            
            <label className="filter-option">
              <input
                type="checkbox"
                checked={filters.duolingoAccepted}
                onChange={() => handleFilterChange('duolingoAccepted')}
              />
              <span>Duolingo Accepted</span>
            </label>
            
            <label className="filter-option">
              <input
                type="checkbox"
                checked={filters.pteAccepted}
                onChange={() => handleFilterChange('pteAccepted')}
              />
              <span>PTE Accepted</span>
            </label>
            
            <label className="filter-option">
              <input
                type="checkbox"
                checked={filters.noGRE}
                onChange={() => handleFilterChange('noGRE')}
              />
              <span>No GRE/GMAT</span>
            </label>
            
            <label className="filter-option">
              <input
                type="checkbox"
                checked={filters.lowTuition}
                onChange={() => handleFilterChange('lowTuition')}
              />
              <span>Tuition < 10K USD</span>
            </label>
          </div>
        </div>
      </div>

      <div className="country-tabs">
        {Object.keys(filteredData).map(country => (
          <button
            key={country}
            className={`country-tab ${activeCountry === country ? 'active' : ''}`}
            onClick={() => setActiveCountry(country)}
          >
            {country}
          </button>
        ))}
      </div>

      {activeCountry && data[activeCountry] && (
        <div className="country-requirements">
          <h2>{activeCountry} - Admission Requirements</h2>
          
          <div className="requirements-grid">
            <div className="requirement-card">
              <div className="requirement-header">
                <FaLanguage className="requirement-icon" />
                <h3>English Language Tests</h3>
              </div>
              <ul className="requirement-list">
                {data[activeCountry].IELTS && (
                  <li><strong>IELTS:</strong> {data[activeCountry].IELTS}</li>
                )}
                {data[activeCountry].TOEFL && (
                  <li><strong>TOEFL:</strong> {data[activeCountry].TOEFL}</li>
                )}
                {data[activeCountry].PTE && (
                  <li><strong>PTE:</strong> {data[activeCountry].PTE}</li>
                )}
                {data[activeCountry].Duolingo && (
                  <li><strong>Duolingo:</strong> {data[activeCountry].Duolingo}</li>
                )}
                {data[activeCountry].Cambridge && (
                  <li><strong>Cambridge:</strong> {data[activeCountry].Cambridge}</li>
                )}
              </ul>
            </div>
            
            <div className="requirement-card">
              <div className="requirement-header">
                <FaGraduationCap className="requirement-icon" />
                <h3>Standardized Tests</h3>
              </div>
              <ul className="requirement-list">
                {data[activeCountry].GRE && (
                  <li><strong>GRE:</strong> {data[activeCountry].GRE}</li>
                )}
                {data[activeCountry].GMAT && (
                  <li><strong>GMAT:</strong> {data[activeCountry].GMAT}</li>
                )}
                {data[activeCountry].SAT && (
                  <li><strong>SAT:</strong> {data[activeCountry].SAT}</li>
                )}
                {data[activeCountry].ACT && (
                  <li><strong>ACT:</strong> {data[activeCountry].ACT}</li>
                )}
              </ul>
            </div>
            
            <div className="requirement-card">
              <div className="requirement-header">
                <FaPassport className="requirement-icon" />
                <h3>Visa & Work Permit</h3>
              </div>
              <ul className="requirement-list">
                {data[activeCountry]['Work Permit'] && (
                  <li><strong>Work Permit:</strong> {data[activeCountry]['Work Permit']}</li>
                )}
                {data[activeCountry]['Visa Processing Time'] && (
                  <li><strong>Visa Processing:</strong> {data[activeCountry]['Visa Processing Time']}</li>
                )}
              </ul>
            </div>
            
            <div className="requirement-card">
              <div className="requirement-header">
                <FaMoneyBillWave className="requirement-icon" />
                <h3>Financial Information</h3>
              </div>
              <ul className="requirement-list">
                {data[activeCountry].Scholarships && (
                  <li><strong>Scholarships:</strong> {data[activeCountry].Scholarships}</li>
                )}
                {data[activeCountry]['Application Fee'] && (
                  <li><strong>Application Fee:</strong> {data[activeCountry]['Application Fee']}</li>
                )}
              </ul>
            </div>
            
            <div className="requirement-card">
              <div className="requirement-header">
                <FaCalendarAlt className="requirement-icon" />
                <h3>Application Timeline</h3>
              </div>
              <ul className="requirement-list">
                {data[activeCountry]['Application Timeline'] && (
                  <li><strong>Timeline:</strong> {data[activeCountry]['Application Timeline']}</li>
                )}
                {data[activeCountry]['Academic Requirements'] && (
                  <li><strong>Academic Requirements:</strong> {data[activeCountry]['Academic Requirements']}</li>
                )}
              </ul>
            </div>
          </div>
        </div>
      )}

      <div className="test-info-section">
        <h2>English Language Tests Comparison</h2>
        <div className="test-comparison-table">
          <table>
            <thead>
              <tr>
                <th>Test</th>
                <th>Score Range</th>
                <th>Duration</th>
                <th>Validity</th>
                <th>Approximate Cost</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>IELTS Academic</td>
                <td>0-9 (band)</td>
                <td>2 hours 45 minutes</td>
                <td>2 years</td>
                <td>$200-250</td>
              </tr>
              <tr>
                <td>TOEFL iBT</td>
                <td>0-120</td>
                <td>3 hours</td>
                <td>2 years</td>
                <td>$180-300</td>
              </tr>
              <tr>
                <td>Duolingo English Test</td>
                <td>10-160</td>
                <td>1 hour</td>
                <td>2 years</td>
                <td>$49</td>
              </tr>
              <tr>
                <td>PTE Academic</td>
                <td>10-90</td>
                <td>2 hours</td>
                <td>2 years</td>
                <td>$180-200</td>
              </tr>
              <tr>
                <td>Cambridge C1 Advanced</td>
                <td>160-210</td>
                <td>4 hours</td>
                <td>Lifetime</td>
                <td>$220-250</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AdmissionGuide;
