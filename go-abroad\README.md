# Go Abroad - Study Abroad Platform

Go Abroad is a comprehensive web application designed to help students explore international education opportunities. The platform provides information about universities worldwide, admission requirements, scholarships, and application processes.

## Features

- **University Search**: Browse universities by region, country, and city with detailed information about programs, tuition fees, and requirements.
- **Admission Guide**: Access comprehensive guides on admission requirements, language tests, and visa procedures for different countries.
- **Scholarships**: Discover scholarships and financial aid opportunities to fund international education.
- **Application Tracking**: Manage university applications in one place, track document submissions, and monitor application status.

## Technology Stack

- **Frontend**: React.js
- **Styling**: Custom CSS with responsive design
- **Icons**: React Icons
- **Data**: JSON data structure

## Project Structure

```
go-abroad/
├── public/
│   ├── index.html
│   └── manifest.json
├── src/
│   ├── assets/
│   │   └── logo.svg
│   ├── components/
│   │   ├── AdmissionGuide.jsx
│   │   ├── Applications.jsx
│   │   ├── Footer.jsx
│   │   ├── Home.jsx
│   │   ├── Scholarships.jsx
│   │   ├── Sidebar.jsx
│   │   ├── TabsComponent.jsx
│   │   ├── UniversityList.jsx
│   │   └── UniversityModal.jsx
│   ├── data/
│   │   ├── admissions.json
│   │   └── universities.json
│   ├── App.jsx
│   ├── index.js
│   └── styles.css
└── package.json
```

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/go-abroad.git
   cd go-abroad
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm start
   ```

4. Open your browser and navigate to `http://localhost:3000`

## Usage

- **Home**: Overview of the platform features and benefits of studying abroad
- **Search Universities**: Browse universities by region, country, and city
- **Scholarships**: Explore scholarship opportunities with filtering options
- **Applications**: Track and manage university applications
- **Admission Guide**: Access comprehensive guides on admission requirements

## Future Enhancements

- User authentication and profile management
- University comparison tool
- Application form submission
- Direct messaging with university representatives
- Community forum for student discussions
- Mobile application

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- University data sourced from public information
- Scholarship information compiled from various educational resources
- Icons provided by React Icons
